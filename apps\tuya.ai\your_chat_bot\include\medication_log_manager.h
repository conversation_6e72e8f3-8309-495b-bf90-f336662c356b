/**
 * @file medication_log_manager.h
 * @brief 服药日志管理模块 - 基于DP118服药记录缓存
 * @version 1.0
 * @date 2024-12-23
 * 
 * 功能特性：
 * - Flash持久化存储（100条记录）
 * - DP118集成上传到前端
 * - JSON格式化和批量上传
 * - 自动同步机制
 * - CRC校验保证数据完整性
 */

#ifndef __MEDICATION_LOG_MANAGER_H__
#define __MEDICATION_LOG_MANAGER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

// Flash存储配置
#define MEDICATION_LOG_FLASH_ADDR           0x1F0000    // Flash存储地址 (1MB-64KB)
#define MEDICATION_LOG_FLASH_SIZE           0x10000     // 64KB存储空间
#define MEDICATION_LOG_MAX_RECORDS          100         // 最大记录数
#define MEDICATION_LOG_RECORD_SIZE          64          // 每条记录大小

// DP118配置
#define MEDICATION_LOG_DP118_ID             118         // DP118: 服药记录缓存
#define MEDICATION_LOG_DP118_CODE           "record"    // DP118代码
#define MEDICATION_LOG_DP118_MAX_LEN        255         // DP118最大长度

// 批量上传配置
#define MEDICATION_LOG_BATCH_SIZE           5           // 批量上传记录数
#define MEDICATION_LOG_UPLOAD_RETRY_MAX     3           // 最大重试次数
#define MEDICATION_LOG_UPLOAD_TIMEOUT_MS    5000        // 上传超时时间

// 药品名称和状态
#define MEDICATION_NAME_MAX_LEN             32          // 药品名称最大长度
#define MEDICATION_STATUS_MAX_LEN           16          // 状态描述最大长度

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 服药状态枚举
 */
typedef enum {
    MEDICATION_STATUS_COMPLETED = 0,    // 已完成
    MEDICATION_STATUS_MISSED,           // 错过
    MEDICATION_STATUS_MAKEUP,           // 补服
    MEDICATION_STATUS_SKIPPED,          // 跳过
    MEDICATION_STATUS_PARTIAL           // 部分完成
} medication_status_e;

/**
 * @brief 服药记录结构体
 */
typedef struct {
    uint32_t record_id;                                 // 记录唯一ID
    uint32_t medicine_id;                               // 药品ID
    char medicine_name[MEDICATION_NAME_MAX_LEN];        // 药品名称
    uint32_t record_time;                               // 服药时间戳
    medication_status_e status;                         // 服药状态
    uint8_t dosage_count;                               // 服药剂量
    char notes[32];                                     // 备注信息
    bool is_uploaded;                                   // 是否已上传
    uint32_t created_time;                              // 创建时间戳
    uint32_t crc32;                                     // CRC32校验值
} medication_log_record_t;

/**
 * @brief 服药日志管理器结构体
 */
typedef struct {
    bool initialized;                                   // 是否已初始化
    uint32_t total_records;                             // 总记录数
    uint32_t uploaded_records;                          // 已上传记录数
    uint32_t pending_records;                           // 待上传记录数
    uint32_t last_upload_time;                          // 最后上传时间
    uint32_t next_record_id;                            // 下一个记录ID
    bool auto_upload_enabled;                           // 自动上传开关
    uint32_t upload_retry_count;                        // 上传重试计数
} medication_log_manager_t;

/**
 * @brief 批量上传结果结构体
 */
typedef struct {
    uint32_t total_count;                               // 总数量
    uint32_t success_count;                             // 成功数量
    uint32_t failed_count;                              // 失败数量
    uint32_t upload_time;                               // 上传时间戳
    char error_message[128];                            // 错误信息
} medication_log_upload_result_t;

/**
 * @brief JSON格式配置结构体
 */
typedef struct {
    bool include_medicine_name;                         // 包含药品名称
    bool include_dosage_count;                          // 包含剂量信息
    bool include_notes;                                 // 包含备注
    bool use_timestamp_format;                          // 使用时间戳格式
    bool compact_format;                                // 紧凑格式
} medication_log_json_config_t;

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化服药日志管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_init(void);

/**
 * @brief 反初始化服药日志管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_manager_deinit(void);

/**
 * @brief 添加服药记录
 * 
 * @param medicine_id 药品ID
 * @param medicine_name 药品名称
 * @param status 服药状态
 * @param dosage_count 服药剂量
 * @param notes 备注信息
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_add_record(uint32_t medicine_id, 
                                     const char *medicine_name,
                                     medication_status_e status,
                                     uint8_t dosage_count,
                                     const char *notes);

/**
 * @brief 获取服药记录
 * 
 * @param record_id 记录ID
 * @param record 记录结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_get_record(uint32_t record_id, medication_log_record_t *record);

/**
 * @brief 获取所有未上传的记录
 * 
 * @param records 记录数组
 * @param max_count 最大数量
 * @param actual_count 实际获取数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_get_pending_records(medication_log_record_t *records, 
                                              uint32_t max_count, 
                                              uint32_t *actual_count);

/**
 * @brief 批量上传记录到DP118
 * 
 * @param records 记录数组
 * @param count 记录数量
 * @param result 上传结果
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_batch_upload(medication_log_record_t *records, 
                                       uint32_t count,
                                       medication_log_upload_result_t *result);

/**
 * @brief 自动上传所有待上传记录
 * 
 * @param result 上传结果
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_auto_upload(medication_log_upload_result_t *result);

/**
 * @brief 将记录格式化为JSON字符串
 * 
 * @param records 记录数组
 * @param count 记录数量
 * @param config JSON配置
 * @param json_str JSON字符串缓冲区
 * @param max_len 最大长度
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_format_json(medication_log_record_t *records,
                                      uint32_t count,
                                      medication_log_json_config_t *config,
                                      char *json_str,
                                      uint32_t max_len);

/**
 * @brief 标记记录为已上传
 * 
 * @param record_id 记录ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_mark_uploaded(uint32_t record_id);

/**
 * @brief 删除记录
 * 
 * @param record_id 记录ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_delete_record(uint32_t record_id);

/**
 * @brief 清空所有记录
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_clear_all(void);

/**
 * @brief 获取管理器统计信息
 * 
 * @param manager 管理器结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_get_stats(medication_log_manager_t *manager);

/**
 * @brief 设置自动上传开关
 * 
 * @param enabled 是否启用自动上传
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_set_auto_upload(bool enabled);

/**
 * @brief 执行Flash存储完整性检查
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_check_integrity(void);

/**
 * @brief 修复损坏的Flash数据
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_repair_flash(void);

/**
 * @brief 导出所有记录为JSON格式
 * 
 * @param json_str JSON字符串缓冲区
 * @param max_len 最大长度
 * @param actual_len 实际长度
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_export_json(char *json_str, uint32_t max_len, uint32_t *actual_len);

/**
 * @brief 从JSON字符串导入记录
 * 
 * @param json_str JSON字符串
 * @param imported_count 导入记录数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_log_import_json(const char *json_str, uint32_t *imported_count);

/**
 * @brief 运行所有测试用例
 * 
 * @return OPERATE_RET 测试结果
 */
OPERATE_RET medication_log_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_LOG_MANAGER_H__ */
