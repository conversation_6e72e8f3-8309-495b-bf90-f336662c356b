/**
 * @file time_manager.h
 * @brief Time management system for UI display
 *
 * This module manages time display, network time synchronization,
 * and provides formatted time strings for UI components.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#ifndef __TIME_MANAGER_H__
#define __TIME_MANAGER_H__

#include "tuya_cloud_types.h"
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

#define TIME_STRING_MAX_LEN         32
#define DATE_STRING_MAX_LEN         32
#define DATETIME_STRING_MAX_LEN     64

// Time format types
typedef enum {
    TIME_FORMAT_24H,        // 14:30
    TIME_FORMAT_12H,        // 2:30 PM
    TIME_FORMAT_24H_SEC,    // 14:30:25
    TIME_FORMAT_12H_SEC     // 2:30:25 PM
} time_format_e;

typedef enum {
    DATE_FORMAT_YYYY_MM_DD,     // 2024-07-21
    DATE_FORMAT_MM_DD_YYYY,     // 07/21/2024
    DATE_FORMAT_DD_MM_YYYY,     // 21/07/2024
    DATE_FORMAT_CHINESE,        // 2024年7月21日
    DATE_FORMAT_WEEKDAY         // 星期日
} date_format_e;

/***********************************************************
***********************typedef define***********************
***********************************************************/

typedef void (*time_update_callback_t)(time_t current_time);

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief Initialize time manager
 *
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_init(void);

/**
 * @brief Update current time (call periodically)
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_update(void);

/**
 * @brief Set network availability status
 * 
 * @param available Network availability status
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_set_network_status(bool available);

/**
 * @brief Sync time from network (NTP)
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_sync_network_time(void);

/**
 * @brief Force sync time with given timestamp
 *
 * @param timestamp Unix timestamp to sync to
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_force_sync(time_t timestamp);

/**
 * @brief Set timezone offset
 *
 * @param offset_hours Timezone offset in hours from UTC (e.g., +8 for Beijing)
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_set_timezone(int offset_hours);

/**
 * @brief Get current time
 * 
 * @return time_t Current time
 */
time_t time_manager_get_current_time(void);

/**
 * @brief Get current time structure
 * 
 * @return struct tm* Pointer to current time structure
 */
struct tm* time_manager_get_current_tm(void);

/**
 * @brief Check if time is synchronized
 * 
 * @return bool True if time is synchronized
 */
bool time_manager_is_time_synced(void);

/**
 * @brief Format current time as string
 * 
 * @param format Time format type
 * @param buffer Buffer to store formatted string
 * @param buffer_size Size of the buffer
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_format_time(time_format_e format, char *buffer, size_t buffer_size);

/**
 * @brief Format current date as string
 * 
 * @param format Date format type
 * @param buffer Buffer to store formatted string
 * @param buffer_size Size of the buffer
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_format_date(date_format_e format, char *buffer, size_t buffer_size);

/**
 * @brief Format current date and time as string
 * 
 * @param time_format Time format type
 * @param date_format Date format type
 * @param buffer Buffer to store formatted string
 * @param buffer_size Size of the buffer
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_format_datetime(time_format_e time_format, date_format_e date_format,
                                        char *buffer, size_t buffer_size);

/**
 * @brief Register time update callback
 * 
 * @param callback Callback function to be called when time updates
 * @return OPERATE_RET Operation result
 */
OPERATE_RET time_manager_register_callback(time_update_callback_t callback);



#ifdef __cplusplus
}
#endif

#endif /* __TIME_MANAGER_H__ */
