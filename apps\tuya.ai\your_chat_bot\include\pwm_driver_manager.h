/**
 * @file pwm_driver_manager.h
 * @brief PWM驱动管理器头文件
 * @version 1.0
 * @date 2025-01-24
 */

#ifndef __PWM_DRIVER_MANAGER_H__
#define __PWM_DRIVER_MANAGER_H__

#include "tuya_cloud_types.h"
#include "tkl_pwm.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

#define PWM_MAX_CHANNELS        8       // 最大PWM通道数
#define PWM_DEFAULT_FREQUENCY   1000    // 默认频率1KHz
#define PWM_MAX_FREQUENCY       100000  // 最大频率100KHz
#define PWM_MIN_FREQUENCY       1       // 最小频率1Hz

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief PWM通道状态枚举
 */
typedef enum {
    PWM_STATE_IDLE = 0,         // 空闲状态
    PWM_STATE_RUNNING,          // 运行状态
    PWM_STATE_ERROR,            // 错误状态
    PWM_STATE_DISABLED          // 禁用状态
} pwm_state_e;

/**
 * @brief PWM应用类型枚举
 */
typedef enum {
    PWM_APP_SERVO = 0,          // 舵机控制
    PWM_APP_LED,                // LED调光
    PWM_APP_BUZZER,             // 蜂鸣器
    PWM_APP_MOTOR,              // 电机控制
    PWM_APP_CUSTOM              // 自定义应用
} pwm_app_type_e;

/**
 * @brief PWM通道配置结构体
 */
typedef struct {
    TUYA_PWM_NUM_E channel;         // PWM通道号
    uint32_t frequency;             // 频率(Hz)
    uint32_t duty_cycle;            // 占空比(0-10000, 表示0.00%-100.00%)
    TUYA_PWM_POLARITY_E polarity;   // 极性
    pwm_app_type_e app_type;        // 应用类型
    bool enabled;                   // 是否启用
    char name[32];                  // 通道名称
} pwm_channel_config_t;

/**
 * @brief PWM通道状态结构体
 */
typedef struct {
    pwm_state_e state;              // 当前状态
    uint32_t start_time;            // 启动时间
    uint32_t total_runtime;         // 总运行时间
    uint32_t error_count;           // 错误计数
    char last_error[64];            // 最后错误信息
} pwm_channel_status_t;

/**
 * @brief PWM管理器结构体
 */
typedef struct {
    bool initialized;                                   // 是否已初始化
    pwm_channel_config_t configs[PWM_MAX_CHANNELS];     // 通道配置
    pwm_channel_status_t status[PWM_MAX_CHANNELS];      // 通道状态
    uint32_t active_channels;                           // 活跃通道掩码
    uint32_t last_update_time;                          // 最后更新时间
} pwm_manager_t;

/**
 * @brief PWM错误回调函数类型
 */
typedef void (*pwm_error_callback_t)(TUYA_PWM_NUM_E channel, const char* error_msg);

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化PWM驱动管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_manager_init(void);

/**
 * @brief 反初始化PWM驱动管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_manager_deinit(void);

/**
 * @brief 配置PWM通道
 * 
 * @param channel PWM通道号
 * @param config 通道配置
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_configure_channel(TUYA_PWM_NUM_E channel, const pwm_channel_config_t* config);

/**
 * @brief 启动PWM通道
 * 
 * @param channel PWM通道号
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_start_channel(TUYA_PWM_NUM_E channel);

/**
 * @brief 停止PWM通道
 * 
 * @param channel PWM通道号
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_stop_channel(TUYA_PWM_NUM_E channel);

/**
 * @brief 设置PWM占空比
 * 
 * @param channel PWM通道号
 * @param duty_cycle 占空比(0-10000)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_set_duty_cycle(TUYA_PWM_NUM_E channel, uint32_t duty_cycle);

/**
 * @brief 设置PWM频率
 * 
 * @param channel PWM通道号
 * @param frequency 频率(Hz)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_set_frequency(TUYA_PWM_NUM_E channel, uint32_t frequency);

/**
 * @brief 获取PWM通道状态
 * 
 * @param channel PWM通道号
 * @param status 状态输出
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_get_channel_status(TUYA_PWM_NUM_E channel, pwm_channel_status_t* status);

/**
 * @brief 获取PWM通道配置
 * 
 * @param channel PWM通道号
 * @param config 配置输出
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_get_channel_config(TUYA_PWM_NUM_E channel, pwm_channel_config_t* config);

/**
 * @brief 同时启动多个PWM通道
 * 
 * @param channels 通道数组
 * @param count 通道数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_start_multiple_channels(TUYA_PWM_NUM_E* channels, uint8_t count);

/**
 * @brief 同时停止多个PWM通道
 * 
 * @param channels 通道数组
 * @param count 通道数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_stop_multiple_channels(TUYA_PWM_NUM_E* channels, uint8_t count);

/**
 * @brief 设置错误回调函数
 * 
 * @param callback 回调函数
 */
void pwm_driver_set_error_callback(pwm_error_callback_t callback);

/**
 * @brief 更新PWM管理器状态
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_update_status(void);

/**
 * @brief 获取PWM管理器统计信息
 * 
 * @param active_count 活跃通道数量输出
 * @param total_runtime 总运行时间输出
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET pwm_driver_get_statistics(uint8_t* active_count, uint32_t* total_runtime);

#ifdef __cplusplus
}
#endif

#endif /* __PWM_DRIVER_MANAGER_H__ */
