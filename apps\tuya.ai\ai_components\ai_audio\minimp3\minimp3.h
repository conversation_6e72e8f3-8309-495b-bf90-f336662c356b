#ifndef MINIMP3_H
#define MINIMP3_H
/*
    https://github.com/lieff/minimp3
    To the extent possible under law, the author(s) have dedicated all copyright and related and neighboring rights to
   this software to the public domain worldwide. This software is distributed without any warranty. See
   <http://creativecommons.org/publicdomain/zero/1.0/>.
*/
#include <stdint.h>
#include <string.h>
#include <limits.h>

#define MINIMP3_MAX_SAMPLES_PER_FRAME (1152*2)
#define HDR_SIZE 4
#define MAX_FREE_FORMAT_FRAME_SIZE 2304
#define MAX_FRAME_SYNC_MATCHES 10
#define MAX_L3_FRAME_PAYLOAD_BYTES MAX_FREE_FORMAT_FRAME_SIZE

#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int frame_bytes, frame_offset, channels, hz, layer, bitrate_kbps;
} mp3dec_frame_info_t;

typedef struct
{
    float mdct_overlap[2][9*32], qmf_state[15*2*32];
    int reserv, free_format_bytes;
    unsigned char header[4], reserv_buf[511];
} mp3dec_t;

#ifdef MINIMP3_FLOAT_OUTPUT
typedef float mp3d_sample_t;
#else
typedef short mp3d_sample_t;
#endif

typedef struct
{
    const uint8_t *buf;
    int pos, limit;
} bs_t;

typedef struct
{
    uint8_t tab_offset, code_tab_width, linbits;
} L3_gr_info_t;

/* decode one mp3 frame, up to 1152 samples returned */
int mp3dec_decode_frame(mp3dec_t *dec, const uint8_t *mp3, int mp3_bytes, mp3d_sample_t *pcm, mp3dec_frame_info_t *info);

/* initialize decoder */
void mp3dec_init(mp3dec_t *dec);

/* helper functions */
static void bs_init(bs_t *bs, const uint8_t *data, int bytes);
static uint32_t get_bits(bs_t *bs, int n);
static int hdr_frame_bytes(const uint8_t *h, int free_format_size);
static int hdr_frame_samples(const uint8_t *h);
static int hdr_padding(const uint8_t *h);
static int hdr_sample_rate_hz(const uint8_t *h);
static int hdr_bitrate_kbps(const uint8_t *h);
static int L3_read_side_info(bs_t *bs, L3_gr_info_t *gr, const uint8_t *hdr);
static int mp3d_find_frame(const uint8_t *mp3, int mp3_bytes, int *free_format_bytes, int *ptr_frame_bytes);

#define MINIMP3_MIN(a, b) ((a) > (b) ? (b) : (a))
#define MINIMP3_MAX(a, b) ((a) < (b) ? (b) : (a))

#define HDR_IS_MONO(h)     (((h[3]) & 0xC0) == 0xC0)
#define HDR_IS_MS_STEREO(h) (((h[3]) & 0xE0) == 0x60)
#define HDR_IS_FREE_FORMAT(h) (((h[2]) & 0xF0) == 0)
#define HDR_IS_CRC(h)      (!((h[1]) & 1))
#define HDR_TEST_PADDING(h) ((h[2]) & 0x2)
#define HDR_TEST_MPEG1(h)  ((h[1]) & 0x8)
#define HDR_TEST_NOT_MPEG25(h) ((h[1]) & 0x10)
#define HDR_TEST_I_STEREO(h) ((h[3]) & 0x10)
#define HDR_TEST_MS_STEREO(h) ((h[3]) & 0x20)
#define HDR_GET_STEREO_MODE(h) (((h[3]) >> 6) & 3)
#define HDR_GET_STEREO_MODE_EXT(h) (((h[3]) >> 4) & 3)
#define HDR_GET_LAYER(h)   (((h[1]) >> 1) & 3)
#define HDR_GET_BITRATE(h) ((h[2]) >> 4)
#define HDR_GET_SAMPLE_RATE(h) (((h[2]) >> 2) & 3)
#define HDR_GET_MY_SAMPLE_RATE(h) (HDR_GET_SAMPLE_RATE(h) + (((h[1] >> 3) & 1) + ((h[1] >> 4) & 1))*3)
#define HDR_IS_LAYER_1(h) (((h[1]) & 6) == 6)
#define HDR_IS_FRAME_576(h) (((h[1]) & 14) == 2)

#ifdef __cplusplus
}
#endif

#ifdef MINIMP3_IMPLEMENTATION

#include <string.h>
#include <limits.h>
#include <stddef.h>
#include <stdlib.h>

/* Basic implementation stubs - these would normally contain the full MP3 decoder implementation */

void mp3dec_init(mp3dec_t *dec)
{
    if (dec) {
        memset(dec, 0, sizeof(*dec));
    }
}

static void bs_init(bs_t *bs, const uint8_t *data, int bytes)
{
    bs->buf = data;
    bs->pos = 0;
    bs->limit = bytes*8;
}

static uint32_t get_bits(bs_t *bs, int n)
{
    uint32_t next, cache = 0, s = bs->pos & 7;
    int shl = n + s;
    const uint8_t *p = bs->buf + (bs->pos >> 3);
    if ((bs->pos += n) > bs->limit)
        return 0;
    next = *p++ & (255 >> s);
    while ((shl -= 8) > 0)
    {
        cache |= next << shl;
        next = *p++;
    }
    return cache | (next >> (-shl));
}

static const uint16_t g_bitrate_tab[15][5] = {
    { 0,0,0,0,0 },
    { 32,32,32,32,8 }, { 64,48,40,48,16 }, { 96,56,48,56,24 }, { 128,64,56,64,32 },
    { 160,80,64,80,40 }, { 192,96,80,96,48 }, { 224,112,96,112,56 }, { 256,128,112,128,64 },
    { 288,160,128,144,80 }, { 320,192,160,160,96 }, { 352,224,192,176,112 }, { 384,256,224,192,128 },
    { 416,320,256,224,144 }, { 448,384,320,256,160 }
};

static const uint16_t g_sample_rate_tab[3] = { 44100, 48000, 32000 };

static int hdr_frame_bytes(const uint8_t *h, int free_format_size)
{
    int frame_bytes = free_format_size;
    if (frame_bytes)
        return frame_bytes;

    int bitrate = HDR_GET_BITRATE(h);
    int sample_rate = HDR_GET_SAMPLE_RATE(h);
    int padding = HDR_TEST_PADDING(h);

    if (!bitrate || bitrate == 15 || !sample_rate || sample_rate == 3)
        return 0;

    sample_rate = g_sample_rate_tab[sample_rate - 1] >> (int)!HDR_TEST_MPEG1(h) >> (int)!HDR_TEST_NOT_MPEG25(h);
    frame_bytes = g_bitrate_tab[bitrate][HDR_TEST_MPEG1(h) ? HDR_GET_LAYER(h) : 4] * 144000;

    if (!frame_bytes)
        return 0;

    switch (HDR_GET_LAYER(h))
    {
        case 1: frame_bytes = (frame_bytes / sample_rate + padding) * 4; break;
        case 2:
        case 3: frame_bytes = frame_bytes / sample_rate + padding; break;
    }
    return frame_bytes;
}

static int hdr_frame_samples(const uint8_t *h)
{
    return HDR_IS_LAYER_1(h) ? 384 : (1152 >> (int)HDR_IS_FRAME_576(h));
}

static int hdr_padding(const uint8_t *h)
{
    return HDR_TEST_PADDING(h);
}

static int hdr_sample_rate_hz(const uint8_t *h)
{
    int sample_rate = HDR_GET_SAMPLE_RATE(h);
    if (sample_rate == 3) return 0;
    return g_sample_rate_tab[sample_rate - 1] >> (int)!HDR_TEST_MPEG1(h) >> (int)!HDR_TEST_NOT_MPEG25(h);
}

static int hdr_bitrate_kbps(const uint8_t *h)
{
    int bitrate = HDR_GET_BITRATE(h);
    if (!bitrate || bitrate == 15) return 0;
    return g_bitrate_tab[bitrate][HDR_TEST_MPEG1(h) ? HDR_GET_LAYER(h) : 4];
}

static int L3_read_side_info(bs_t *bs, L3_gr_info_t *gr, const uint8_t *hdr)
{
    /* Simplified implementation */
    (void)bs; (void)gr; (void)hdr;
    return 0;
}

static int mp3d_find_frame(const uint8_t *mp3, int mp3_bytes, int *free_format_bytes, int *ptr_frame_bytes)
{
    /* Simplified frame finding implementation */
    if (!mp3 || mp3_bytes < HDR_SIZE) {
        if (ptr_frame_bytes) *ptr_frame_bytes = 0;
        if (free_format_bytes) *free_format_bytes = 0;
        return 0;
    }

    /* Look for sync word */
    for (int i = 0; i < mp3_bytes - HDR_SIZE; i++) {
        if (mp3[i] == 0xFF && (mp3[i+1] & 0xE0) == 0xE0) {
            int frame_bytes = hdr_frame_bytes(mp3 + i, 0);
            if (frame_bytes > 0) {
                if (ptr_frame_bytes) *ptr_frame_bytes = frame_bytes;
                if (free_format_bytes) *free_format_bytes = 0;
                return i;
            }
        }
    }

    if (ptr_frame_bytes) *ptr_frame_bytes = 0;
    if (free_format_bytes) *free_format_bytes = 0;
    return mp3_bytes;
}

int mp3dec_decode_frame(mp3dec_t *dec, const uint8_t *mp3, int mp3_bytes, mp3d_sample_t *pcm, mp3dec_frame_info_t *info)
{
    /* This is a stub implementation - in a real scenario, this would contain the full MP3 decoder */
    if (!dec || !mp3 || !pcm || !info || mp3_bytes <= 0) {
        return 0;
    }

    /* Initialize frame info */
    memset(info, 0, sizeof(*info));

    /* For now, return 0 samples (no decode) */
    return 0;
}

#endif /* MINIMP3_IMPLEMENTATION */

#endif /* MINIMP3_H */
