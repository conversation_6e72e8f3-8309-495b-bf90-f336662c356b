/**
 * @file chinese_font_test.h
 * @brief 中文字体测试程序头文件
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef CHINESE_FONT_TEST_H
#define CHINESE_FONT_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

/**
 * @brief 测试中文字体是否正确加载
 */
void test_chinese_font_loading(void);

/**
 * @brief 创建测试标签验证显示效果
 * @param parent 父对象
 * @param text 文本内容
 * @return 创建的标签对象
 */
lv_obj_t* create_chinese_test_label(lv_obj_t *parent, const char *text);

/**
 * @brief 运行完整的中文字体测试
 */
void run_chinese_font_test(void);

#ifdef __cplusplus
}
#endif

#endif /* CHINESE_FONT_TEST_H */
