/**
 * @file iot_property_manager.h
 * @brief IoT属性管理器头文件
 * @version 1.0
 * @date 2025-01-24
 */

#ifndef __IOT_PROPERTY_MANAGER_H__
#define __IOT_PROPERTY_MANAGER_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化IoT属性管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_property_manager_init(void);

/**
 * @brief 更新药物相关属性
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_property_update_medication_status(void);

/**
 * @brief 更新硬件状态属性
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_property_update_hardware_status(void);

/**
 * @brief 更新系统状态属性
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_property_update_system_status(void);

/**
 * @brief 定期更新所有属性
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_property_manager_update_all(void);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_PROPERTY_MANAGER_H__ */
