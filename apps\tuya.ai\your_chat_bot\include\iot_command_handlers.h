/**
 * @file iot_command_handlers.h
 * @brief IoT命令处理函数头文件
 * @version 1.0
 * @date 2025-01-24
 */

#ifndef __IOT_COMMAND_HANDLERS_H__
#define __IOT_COMMAND_HANDLERS_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化IoT命令处理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_command_handlers_init(void);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_COMMAND_HANDLERS_H__ */
