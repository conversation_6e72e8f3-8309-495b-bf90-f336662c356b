/**
 * @file ble_config_manager.h
 * @brief BLE配网管理器头文件 - 解决BLE配网问题
 * @version 1.0
 * @date 2025-07-25
 */

#ifndef __BLE_CONFIG_MANAGER_H__
#define __BLE_CONFIG_MANAGER_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
***********************typedef define***********************
***********************************************************/

typedef enum {
    BLE_CONFIG_EVENT_STARTED,      // 配网开始
    BLE_CONFIG_EVENT_CONNECTED,    // BLE连接成功
    BLE_CONFIG_EVENT_CONFIGURING,  // 正在配网
    BLE_CONFIG_EVENT_SUCCESS,      // 配网成功
    BLE_CONFIG_EVENT_FAILED,       // 配网失败
    BLE_CONFIG_EVENT_TIMEOUT,      // 配网超时
    BLE_CONFIG_EVENT_STOPPED       // 配网停止
} ble_config_event_t;

typedef struct {
    uint32_t adv_start_count;       // 广播启动次数
    uint32_t connect_count;         // 连接次数
    uint32_t config_success_count;  // 配网成功次数
    uint32_t config_failed_count;   // 配网失败次数
    uint32_t retry_count;           // 重试次数
    uint32_t elapsed_time_ms;       // 运行时间
    int current_state;              // 当前状态
} ble_config_statistics_t;

typedef void (*ble_config_event_callback_t)(ble_config_event_t event, void *data);

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化BLE配网管理器
 * 
 * @param callback 事件回调函数
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET ble_config_manager_init(ble_config_event_callback_t callback);

/**
 * @brief 开始BLE配网
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET ble_config_manager_start(void);

/**
 * @brief 停止BLE配网
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET ble_config_manager_stop(void);

/**
 * @brief 重试BLE配网
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET ble_config_manager_retry(void);

/**
 * @brief 获取BLE配网状态
 * 
 * @return int 当前状态
 */
int ble_config_manager_get_state(void);

/**
 * @brief 获取BLE配网统计信息
 * 
 * @param stats 统计信息结构体
 */
void ble_config_manager_get_statistics(ble_config_statistics_t *stats);

/**
 * @brief 打印BLE配网状态
 */
void ble_config_manager_print_status(void);

#ifdef __cplusplus
}
#endif

#endif /* __BLE_CONFIG_MANAGER_H__ */
