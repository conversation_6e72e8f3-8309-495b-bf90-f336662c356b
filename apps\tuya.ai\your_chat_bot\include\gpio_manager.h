/**
 * @file gpio_manager.h
 * @brief GPIO管理模块 - 基于涂鸦开发板硬件资源
 * @version 1.0
 * @date 2024-12-23
 * 
 * 功能特性：
 * - LED控制系统（4种状态：关闭/常亮/慢闪/快闪）
 * - 按键管理（防抖处理、长按检测、事件回调）
 * - GPIO状态更新（实时监控，定期更新10ms周期）
 * - 硬件抽象（标准化的GPIO操作接口）
 * 
 * 硬件配置：
 * - 系统LED: P01 (TUYA_GPIO_NUM_1)
 * - 用户按键: P12 (TUYA_GPIO_NUM_12)
 * - PWM引脚: P06-P08 (预留舵机控制)
 */

#ifndef __GPIO_MANAGER_H__
#define __GPIO_MANAGER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

// GPIO引脚定义 (基于开发板硬件资源)
#define GPIO_LED_USER               TUYA_GPIO_NUM_1     // P01 - 开发板User LED
#define GPIO_BUTTON_USER            TUYA_GPIO_NUM_12    // P12 - 开发板User按键
#define SERVO_PWM_PIN_1             TUYA_GPIO_NUM_6     // P06 - pwm0:0 (预留)
#define SERVO_PWM_PIN_2             TUYA_GPIO_NUM_7     // P07 - pwm0:1 (预留)
#define SERVO_PWM_PIN_3             TUYA_GPIO_NUM_8     // P08 - pwm0:2 (预留)

// 按键配置
#define BUTTON_DEBOUNCE_TIME_MS     50                  // 防抖时间(毫秒)
#define BUTTON_LONG_PRESS_TIME_MS   2000                // 长按时间(毫秒)

// LED闪烁配置
#define LED_SLOW_BLINK_INTERVAL_MS  1000                // 慢闪间隔(毫秒)
#define LED_FAST_BLINK_INTERVAL_MS  200                 // 快闪间隔(毫秒)

// GPIO更新周期
#define GPIO_UPDATE_INTERVAL_MS     10                  // GPIO状态更新间隔(毫秒)

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief LED状态枚举
 */
typedef enum {
    LED_STATE_OFF = 0,          // 关闭
    LED_STATE_ON,               // 常亮
    LED_STATE_SLOW_BLINK,       // 慢闪 (1秒间隔)
    LED_STATE_FAST_BLINK        // 快闪 (0.2秒间隔)
} led_state_e;

/**
 * @brief 按键事件枚举
 */
typedef enum {
    BUTTON_EVENT_NONE = 0,      // 无事件
    BUTTON_EVENT_PRESS,         // 按下
    BUTTON_EVENT_RELEASE,       // 释放
    BUTTON_EVENT_CLICK,         // 单击
    BUTTON_EVENT_LONG_PRESS,    // 长按
    BUTTON_EVENT_DOUBLE_CLICK   // 双击
} button_event_e;

/**
 * @brief 按键状态枚举
 */
typedef enum {
    BUTTON_STATE_IDLE = 0,      // 空闲
    BUTTON_STATE_PRESSED,       // 按下
    BUTTON_STATE_DEBOUNCE,      // 防抖中
    BUTTON_STATE_LONG_PRESS     // 长按中
} button_state_e;

/**
 * @brief GPIO管理器状态结构体
 */
typedef struct {
    bool initialized;                   // 是否已初始化
    led_state_e system_led_state;       // 系统LED状态
    uint32_t last_blink_time;           // 最后闪烁时间
    bool led_current_state;             // LED当前物理状态
    uint32_t last_update_time;          // 最后更新时间
    uint32_t update_count;              // 更新次数统计
} gpio_manager_t;

/**
 * @brief 按键状态结构体
 */
typedef struct {
    button_state_e state;               // 按键状态
    bool current_level;                 // 当前电平
    bool last_level;                    // 上次电平
    uint32_t press_time;                // 按下时间
    uint32_t release_time;              // 释放时间
    uint32_t debounce_time;             // 防抖时间
    uint32_t click_count;               // 点击计数
    uint32_t last_click_time;           // 最后点击时间
    bool long_press_triggered;          // 长按是否已触发
} button_status_t;

/**
 * @brief GPIO统计信息结构体
 */
typedef struct {
    uint32_t led_state_changes;         // LED状态变化次数
    uint32_t button_press_count;        // 按键按下次数
    uint32_t button_click_count;        // 按键点击次数
    uint32_t button_long_press_count;   // 按键长按次数
    uint32_t gpio_update_count;         // GPIO更新次数
    uint32_t last_activity_time;        // 最后活动时间
} gpio_statistics_t;

/**
 * @brief 按键事件回调函数类型
 */
typedef void (*button_event_callback_t)(button_event_e event);

/**
 * @brief LED状态变化回调函数类型
 */
typedef void (*led_state_callback_t)(led_state_e old_state, led_state_e new_state);

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化GPIO管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_init(void);

/**
 * @brief 反初始化GPIO管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_deinit(void);

/**
 * @brief 设置系统LED状态
 * 
 * @param state LED状态
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_set_system_led(led_state_e state);

/**
 * @brief 获取系统LED状态
 * 
 * @return led_state_e LED状态
 */
led_state_e gpio_manager_get_system_led(void);

/**
 * @brief 获取LED当前物理状态
 * 
 * @return bool LED物理状态 (true=亮, false=灭)
 */
bool gpio_manager_get_led_physical_state(void);

/**
 * @brief 初始化按键
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_init_button(void);

/**
 * @brief 获取按键状态
 * 
 * @param status 按键状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_get_button_status(button_status_t *status);

/**
 * @brief 检查按键事件
 * 
 * @return button_event_e 按键事件
 */
button_event_e gpio_manager_check_button_event(void);

/**
 * @brief 设置按键事件回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_set_button_callback(button_event_callback_t callback);

/**
 * @brief 设置LED状态变化回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_set_led_callback(led_state_callback_t callback);

/**
 * @brief 更新GPIO状态 (定期调用，建议10ms间隔)
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_update(void);

/**
 * @brief 获取管理器状态
 * 
 * @param manager 管理器状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_get_status(gpio_manager_t *manager);

/**
 * @brief 获取统计信息
 * 
 * @param statistics 统计信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_get_statistics(gpio_statistics_t *statistics);

/**
 * @brief 重置统计信息
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_reset_statistics(void);

/**
 * @brief 获取LED状态字符串
 * 
 * @param state LED状态
 * @return const char* 状态字符串
 */
const char* gpio_manager_get_led_state_string(led_state_e state);

/**
 * @brief 获取按键事件字符串
 * 
 * @param event 按键事件
 * @return const char* 事件字符串
 */
const char* gpio_manager_get_button_event_string(button_event_e event);

/**
 * @brief 强制LED状态 (忽略闪烁逻辑)
 * 
 * @param on LED状态 (true=亮, false=灭)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_force_led_state(bool on);

/**
 * @brief 读取按键原始电平
 * 
 * @return bool 按键电平 (true=高电平, false=低电平)
 */
bool gpio_manager_read_button_raw(void);

/**
 * @brief 测试LED闪烁功能
 * 
 * @param duration_ms 测试持续时间(毫秒)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_test_led_blink(uint32_t duration_ms);

/**
 * @brief 测试按键功能
 * 
 * @param timeout_ms 测试超时时间(毫秒)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET gpio_manager_test_button(uint32_t timeout_ms);

/**
 * @brief 运行所有测试用例
 * 
 * @return OPERATE_RET 测试结果
 */
OPERATE_RET gpio_manager_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __GPIO_MANAGER_H__ */
