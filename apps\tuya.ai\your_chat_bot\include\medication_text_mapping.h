/**
 * @file medication_text_mapping.h
 * @brief 药物名称文本映射头文件 - 解决中文显示问题
 * @version 1.0
 * @date 2025-07-25
 */

#ifndef __MEDICATION_TEXT_MAPPING_H__
#define __MEDICATION_TEXT_MAPPING_H__

#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 获取中文药物名称的显示映射
 * 
 * @param chinese_name 中文药物名称
 * @return const char* 映射后的显示名称（英文或可显示的文本）
 */
const char* medication_text_mapping_get_display_name(const char *chinese_name);

/**
 * @brief 安全设置标签文本（自动映射中文）
 * 
 * @param label LVGL标签对象
 * @param chinese_text 中文文本
 */
void medication_text_mapping_safe_set_text(lv_obj_t *label, const char *chinese_text);

/**
 * @brief 初始化药物文本映射系统
 * 
 * @return int 操作结果
 */
int medication_text_mapping_init(void);

/**
 * @brief 打印所有映射关系（调试用）
 */
void medication_text_mapping_print_all_mappings(void);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_TEXT_MAPPING_H__ */
