/**
 * @file wifi_manager.h
 * @brief WiFi管理模块头文件
 * 
 * 基于tkl_wifi API实现的WiFi管理功能，包括：
 * - WiFi扫描和连接管理
 * - 信号强度监控
 * - 配网状态管理
 * - 网络质量评估
 * 
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 */

#ifndef __WIFI_MANAGER_H__
#define __WIFI_MANAGER_H__

#include "tuya_cloud_types.h"
#include "tal_wifi.h"
#include "tkl_wifi.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/
#define WIFI_SCAN_MAX_AP_NUM        20      // 最大扫描AP数量
#define WIFI_RSSI_EXCELLENT         -50     // 信号强度：优秀
#define WIFI_RSSI_GOOD              -60     // 信号强度：良好
#define WIFI_RSSI_FAIR              -70     // 信号强度：一般
#define WIFI_RSSI_POOR              -80     // 信号强度：较差
#define WIFI_MONITOR_INTERVAL_MS    5000    // 监控间隔(ms)

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief WiFi信号强度等级
 */
typedef enum {
    WIFI_SIGNAL_EXCELLENT = 0,     // 优秀 (-50dBm以上)
    WIFI_SIGNAL_GOOD,               // 良好 (-50~-60dBm)
    WIFI_SIGNAL_FAIR,               // 一般 (-60~-70dBm)
    WIFI_SIGNAL_POOR,               // 较差 (-70~-80dBm)
    WIFI_SIGNAL_VERY_POOR,          // 很差 (-80dBm以下)
    WIFI_SIGNAL_UNKNOWN             // 未知
} wifi_signal_level_e;

/**
 * @brief WiFi连接状态
 */
typedef enum {
    WIFI_STATUS_DISCONNECTED = 0,   // 未连接
    WIFI_STATUS_CONNECTING,         // 连接中
    WIFI_STATUS_CONNECTED,          // 已连接
    WIFI_STATUS_FAILED,             // 连接失败
    WIFI_STATUS_CONFIG_MODE         // 配网模式
} wifi_status_e;

/**
 * @brief WiFi安全类型
 */
typedef enum {
    WIFI_SECURITY_OPEN = 0,         // 开放网络
    WIFI_SECURITY_WEP,              // WEP加密
    WIFI_SECURITY_WPA,              // WPA加密
    WIFI_SECURITY_WPA2,             // WPA2加密
    WIFI_SECURITY_WPA3              // WPA3加密
} wifi_security_type_e;

/**
 * @brief WiFi网络信息
 */
typedef struct {
    char ssid[33];                  // SSID
    uint8_t bssid[6];               // BSSID (MAC地址)
    int8_t rssi;                    // 信号强度
    uint8_t channel;                // 信道
    wifi_signal_level_e signal_level; // 信号等级
    wifi_security_type_e security_type; // 安全类型
    bool is_connected;              // 是否已连接
    NW_IP_S ip_info;                // IP信息
} wifi_network_info_t;

/**
 * @brief WiFi状态回调函数
 */
typedef void (*wifi_status_callback_t)(wifi_status_e status, const wifi_network_info_t *info);

/**
 * @brief WiFi扫描结果回调函数
 */
typedef void (*wifi_scan_callback_t)(const wifi_network_info_t *networks, uint32_t count);

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化WiFi管理器
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_init(void);

/**
 * @brief 注册WiFi状态回调
 * 
 * @param callback 状态回调函数
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_register_status_callback(wifi_status_callback_t callback);

/**
 * @brief 扫描WiFi网络
 *
 * @param callback 扫描结果回调
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_scan_networks(wifi_scan_callback_t callback);

/**
 * @brief 连接WiFi网络（支持快连）
 *
 * @param ssid WiFi名称
 * @param password WiFi密码
 * @param use_fast_connect 是否使用快连
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_connect_network(const char *ssid, const char *password, bool use_fast_connect);

/**
 * @brief 断开WiFi连接
 *
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_disconnect(void);

/**
 * @brief 获取当前连接的AP信息
 *
 * @param info 网络信息结构体
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_get_connected_ap_info(wifi_network_info_t *info);

/**
 * @brief 获取当前连接的WiFi信息
 *
 * @param info 网络信息结构体
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_get_current_network(wifi_network_info_t *info);

/**
 * @brief 获取WiFi连接状态
 * 
 * @return wifi_status_e 
 */
wifi_status_e wifi_manager_get_status(void);

/**
 * @brief 获取信号强度等级
 * 
 * @param rssi 信号强度值
 * @return wifi_signal_level_e 
 */
wifi_signal_level_e wifi_manager_get_signal_level(int8_t rssi);

/**
 * @brief 获取信号强度等级字符串
 * 
 * @param level 信号等级
 * @return const char* 
 */
const char* wifi_manager_get_signal_level_string(wifi_signal_level_e level);

/**
 * @brief 开始WiFi监控
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_start_monitor(void);

/**
 * @brief 停止WiFi监控
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_stop_monitor(void);

/**
 * @brief 触发配网模式
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_enter_config_mode(void);

/**
 * @brief 退出配网模式
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_exit_config_mode(void);

/**
 * @brief 设置WiFi低功耗模式
 *
 * @param enable 是否启用低功耗
 * @param dtim DTIM参数
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_set_low_power_mode(bool enable, uint8_t dtim);

/**
 * @brief 获取WiFi MAC地址
 *
 * @param mac MAC地址缓冲区(6字节)
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_get_mac_address(uint8_t mac[6]);

/**
 * @brief 获取当前WiFi信道
 *
 * @param channel 信道号
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_get_current_channel(uint8_t *channel);

/**
 * @brief 设置国家码
 *
 * @param country_code 国家码
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_set_country_code(COUNTRY_CODE_E country_code);

/**
 * @brief 获取WiFi统计信息
 *
 * @param scan_count 扫描次数
 * @param connect_count 连接次数
 * @param disconnect_count 断开次数
 * @return OPERATE_RET
 */
OPERATE_RET wifi_manager_get_statistics(uint32_t *scan_count, uint32_t *connect_count, uint32_t *disconnect_count);

/**
 * @brief 运行WiFi管理器测试
 * 
 * @return OPERATE_RET 
 */
OPERATE_RET wifi_manager_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __WIFI_MANAGER_H__ */
