/**
 * @file display_stability_fix.h
 * @brief 显示稳定性修复模块头文件
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef DISPLAY_STABILITY_FIX_H
#define DISPLAY_STABILITY_FIX_H

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 显示稳定性配置结构体
 */
typedef struct {
    bool anti_flicker_enabled;         // 是否启用防闪烁
    uint8_t refresh_rate_limit;        // 刷新率限制(Hz)
    uint32_t message_debounce_ms;      // 消息防抖时间(ms)
    uint32_t status_update_interval_ms; // 状态更新间隔(ms)
    bool force_single_buffer;          // 强制单缓冲
} display_stability_config_t;

/**
 * @brief 初始化显示稳定性修复
 * @return OPERATE_RET 
 */
OPERATE_RET display_stability_init(void);

/**
 * @brief 检查状态消息是否需要更新（防抖）
 * @param status 状态消息
 * @return true 需要更新，false 跳过更新
 */
bool display_stability_should_update_status(const char *status);

/**
 * @brief 检查通知消息是否需要更新（防抖）
 * @param notification 通知消息
 * @return true 需要更新，false 跳过更新
 */
bool display_stability_should_update_notification(const char *notification);

/**
 * @brief 强制刷新显示器（用于重要更新）
 */
void display_stability_force_refresh(void);

/**
 * @brief 设置防闪烁配置
 * @param config 配置结构体
 */
void display_stability_set_config(const display_stability_config_t *config);

/**
 * @brief 获取当前配置
 * @return const display_stability_config_t* 
 */
const display_stability_config_t* display_stability_get_config(void);

/**
 * @brief 清空消息状态缓存
 */
void display_stability_clear_cache(void);

/**
 * @brief 优化显示性能（配网时使用）
 */
void display_stability_optimize_performance(void);

/**
 * @brief 恢复正常显示性能
 */
void display_stability_restore_performance(void);

#ifdef __cplusplus
}
#endif

#endif /* DISPLAY_STABILITY_FIX_H */
