/**
 * @file display_optimization.h
 * @brief 显示优化配置头文件 - 减少屏幕闪烁
 * @version 1.0
 * @date 2025-07-25
 */

#ifndef __DISPLAY_OPTIMIZATION_H__
#define __DISPLAY_OPTIMIZATION_H__

#include "tuya_cloud_types.h"
#include "lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化显示优化设置
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET display_optimization_init(void);

/**
 * @brief 优化标签显示，减少闪烁
 * 
 * @param label 标签对象
 */
void display_optimization_setup_label(lv_obj_t *label);

/**
 * @brief 优化容器显示
 * 
 * @param container 容器对象
 */
void display_optimization_setup_container(lv_obj_t *container);

/**
 * @brief 安全更新标签文本，避免不必要的重绘
 * 
 * @param label 标签对象
 * @param new_text 新文本
 */
void display_optimization_update_label_text(lv_obj_t *label, const char *new_text);

/**
 * @brief 批量更新显示，减少重绘次数
 */
void display_optimization_batch_update_start(void);

/**
 * @brief 结束批量更新，恢复显示刷新
 */
void display_optimization_batch_update_end(void);

/**
 * @brief 减少动画效果，提高性能
 */
void display_optimization_disable_animations(void);

/**
 * @brief 恢复正常动画效果
 */
void display_optimization_enable_animations(void);

#ifdef __cplusplus
}
#endif

#endif /* __DISPLAY_OPTIMIZATION_H__ */
