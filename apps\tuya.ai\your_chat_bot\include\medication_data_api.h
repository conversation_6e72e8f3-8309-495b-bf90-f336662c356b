/**
 * @file medication_data_api.h
 * @brief 药物数据接口 - 供外部系统调用
 * @version 1.0
 * @date 2025-07-22
 */

#ifndef __MEDICATION_DATA_API_H__
#define __MEDICATION_DATA_API_H__

#include "tuya_cloud_types.h"
#include "medication_manager.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

#define MEDICATION_API_SUCCESS          0
#define MEDICATION_API_ERROR           -1
#define MEDICATION_API_INVALID_PARAM   -2
#define MEDICATION_API_NOT_FOUND       -3
#define MEDICATION_API_FULL            -4

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 简化的药物信息结构体（供外部使用）
 */
typedef struct {
    char name[32];              // 药物名称
    char time_str[64];          // 服药时间字符串 (如: "08:00-08:30 14:00-14:30")
    char dosage[16];            // 剂量 (如: "1片", "2粒")
    int box_number;             // 药盒编号 (1-6)
    bool is_taken;              // 是否已服用
} simple_medication_t;

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 清空所有药物数据
 * 
 * @return int 0:成功, <0:失败
 */
int medication_api_clear_all(void);

/**
 * @brief 添加单个药物
 * 
 * @param name 药物名称
 * @param time_str 服药时间 (格式: "08:00-08:30" 或 "08:00-08:30 14:00-14:30")
 * @param dosage 剂量 (如: "1片", "2粒")
 * @param box_number 药盒编号 (1-6)
 * @return int 0:成功, <0:失败
 */
int medication_api_add_single(const char *name, const char *time_str, const char *dosage, int box_number);

/**
 * @brief 批量设置药物数据
 * 
 * @param medications 药物数组
 * @param count 药物数量
 * @return int 0:成功, <0:失败
 */
int medication_api_set_batch(simple_medication_t *medications, int count);

/**
 * @brief 获取当前药物数量
 * 
 * @return int 药物数量
 */
int medication_api_get_count(void);

/**
 * @brief 获取所有药物信息
 * 
 * @param medications 输出药物数组
 * @param max_count 最大数量
 * @return int 实际获取的数量
 */
int medication_api_get_all(simple_medication_t *medications, int max_count);

/**
 * @brief 刷新UI显示
 * 
 * @return int 0:成功, <0:失败
 */
int medication_api_refresh_ui(void);

/**
 * @brief 从JSON字符串加载药物数据
 * 
 * @param json_str JSON格式的药物数据
 * @return int 0:成功, <0:失败
 */
int medication_api_load_from_json(const char *json_str);

/**
 * @brief 导出药物数据为JSON字符串
 *
 * @param json_buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return int 0:成功, <0:失败
 */
int medication_api_export_to_json(char *json_buffer, int buffer_size);

/**
 * @brief 演示API使用方法（测试函数）
 *
 * @return int 0:成功, <0:失败
 */
int medication_api_demo_update(void);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_DATA_API_H__ */
