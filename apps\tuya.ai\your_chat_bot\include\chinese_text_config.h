/**
 * @file chinese_text_config.h
 * @brief Chinese text display configuration
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef CHINESE_TEXT_CONFIG_H
#define CHINESE_TEXT_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"
#include "font_puhui_16_2.h"

/* 中文文本配置 */
#define CHINESE_TEXT_ENCODING_UTF8      1
#define CHINESE_TEXT_FONT_SIZE_16       16
#define CHINESE_TEXT_FONT_SIZE_18       18
#define CHINESE_TEXT_FONT_SIZE_24       24

/* 常用中文文本定义 */
#define TEXT_INITIALIZING               "正在初始化..."
#define TEXT_CONNECTING                 "正在连接网络..."
#define TEXT_CONNECTED                  "网络连接成功"
#define TEXT_DISCONNECTED               "网络连接断开"
#define TEXT_LISTENING                  "正在听中..."
#define TEXT_PROCESSING                 "正在处理中..."
#define TEXT_SPEAKING                   "正在说话中..."
#define TEXT_WAITING                    "等待命令..."
#define TEXT_ERROR                      "系统错误"
#define TEXT_READY                      "就绪"
#define TEXT_VOLUME                     "音量"
#define TEXT_SETTINGS                   "设置"
#define TEXT_BACK                       "返回"
#define TEXT_CONFIRM                    "确认"
#define TEXT_CANCEL                     "取消"
#define TEXT_CHAT_BOT                   "聊天机器人"
#define TEXT_VOICE_ASSISTANT            "语音助手"
#define TEXT_SMART_DEVICE               "智能设备"
#define TEXT_CLOUD_SERVICE              "云端服务"
#define TEXT_AI_ASSISTANT               "AI助手"
#define TEXT_WELCOME                    "欢迎使用"
#define TEXT_GOODBYE                    "再见"
#define TEXT_THANK_YOU                  "谢谢"
#define TEXT_PLEASE_WAIT                "请稍等"
#define TEXT_TRY_AGAIN                  "请重试"
#define TEXT_NETWORK_ERROR              "网络错误"
#define TEXT_AUDIO_ERROR                "音频错误"
#define TEXT_SYSTEM_BUSY                "系统繁忙"
#define TEXT_TIMEOUT                    "超时"
#define TEXT_SUCCESS                    "成功"
#define TEXT_FAILED                     "失败"

/* 状态文本 */
#define TEXT_STATUS_IDLE                "空闲"
#define TEXT_STATUS_BUSY                "忙碌"
#define TEXT_STATUS_ONLINE              "在线"
#define TEXT_STATUS_OFFLINE             "离线"
#define TEXT_STATUS_RECORDING           "录音中"
#define TEXT_STATUS_PLAYING             "播放中"

/* 菜单文本 */
#define TEXT_MENU_MAIN                  "主菜单"
#define TEXT_MENU_CHAT                  "聊天"
#define TEXT_MENU_SETTINGS              "设置"
#define TEXT_MENU_ABOUT                 "关于"
#define TEXT_MENU_EXIT                  "退出"

/* 设置项文本 */
#define TEXT_SETTING_VOLUME             "音量设置"
#define TEXT_SETTING_LANGUAGE           "语言设置"
#define TEXT_SETTING_NETWORK            "网络设置"
#define TEXT_SETTING_DISPLAY            "显示设置"
#define TEXT_SETTING_AUDIO              "音频设置"

/* 错误信息文本 */
#define TEXT_ERROR_NETWORK              "网络连接失败"
#define TEXT_ERROR_AUDIO_INIT           "音频初始化失败"
#define TEXT_ERROR_DISPLAY_INIT         "显示初始化失败"
#define TEXT_ERROR_MEMORY               "内存不足"
#define TEXT_ERROR_FILE_NOT_FOUND       "文件未找到"
#define TEXT_ERROR_PERMISSION           "权限不足"
#define TEXT_ERROR_TIMEOUT              "操作超时"
#define TEXT_ERROR_INVALID_PARAM        "参数无效"

/* 提示信息文本 */
#define TEXT_TIP_SPEAK_NOW              "请开始说话"
#define TEXT_TIP_SPEAK_LOUDER           "请大声一点"
#define TEXT_TIP_CHECK_NETWORK          "请检查网络连接"
#define TEXT_TIP_RESTART_DEVICE         "请重启设备"
#define TEXT_TIP_CONTACT_SUPPORT        "请联系技术支持"

/* 时间相关文本 */
#define TEXT_TIME_MORNING               "早上好"
#define TEXT_TIME_AFTERNOON             "下午好"
#define TEXT_TIME_EVENING               "晚上好"
#define TEXT_TIME_NIGHT                 "晚安"

/* 天气相关文本 */
#define TEXT_WEATHER_SUNNY              "晴天"
#define TEXT_WEATHER_CLOUDY             "多云"
#define TEXT_WEATHER_RAINY              "雨天"
#define TEXT_WEATHER_SNOWY              "雪天"

/* 智能家居相关文本 */
#define TEXT_LIGHT_ON                   "灯已打开"
#define TEXT_LIGHT_OFF                  "灯已关闭"
#define TEXT_TEMPERATURE                "温度"
#define TEXT_HUMIDITY                   "湿度"
#define TEXT_AIR_QUALITY                "空气质量"

/* 字体配置宏 */
#define CHINESE_FONT_SMALL              &font_puhui_16_2
#define CHINESE_FONT_MEDIUM             &font_puhui_16_2
#define CHINESE_FONT_LARGE              &font_puhui_16_2

/* 文本样式配置 */
typedef struct {
    const lv_font_t *font;
    lv_color_t color;
    lv_text_align_t align;
    uint16_t line_space;
    uint16_t letter_space;
} chinese_text_style_t;

/* 预定义文本样式 */
extern const chinese_text_style_t chinese_style_title;
extern const chinese_text_style_t chinese_style_content;
extern const chinese_text_style_t chinese_style_button;
extern const chinese_text_style_t chinese_style_status;

/* 函数声明 */
void chinese_text_init(void);
void chinese_text_set_encoding(uint8_t encoding);
lv_obj_t* chinese_label_create(lv_obj_t *parent, const char *text, const chinese_text_style_t *style);
void chinese_label_set_text(lv_obj_t *label, const char *text);
void chinese_text_apply_style(lv_obj_t *obj, const chinese_text_style_t *style);

/* UTF-8字符串处理函数 */
bool chinese_text_contains_chinese(const char *str);
uint32_t chinese_text_get_char_count(const char *str);
uint32_t chinese_text_truncate(char *dest, const char *src, uint32_t max_chars, uint32_t dest_size);

/* 英文替代文本函数 */
const char* get_english_fallback(const char *chinese_text);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*CHINESE_TEXT_CONFIG_H*/
