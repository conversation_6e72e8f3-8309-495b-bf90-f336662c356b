/**
 * @file audio_alert_generator.h
 * @brief 音频提示音生成器头文件
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef AUDIO_ALERT_GENERATOR_H
#define AUDIO_ALERT_GENERATOR_H

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 播放配网提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_netconfig(void);

/**
 * @brief 播放网络连接成功提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_network_connected(void);

/**
 * @brief 播放唤醒提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_wakeup(void);

/**
 * @brief 播放网络连接失败提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_network_failed(void);

/**
 * @brief 播放开机提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_power_on(void);

/**
 * @brief 播放按键提示音
 * @return OPERATE_RET 
 */
OPERATE_RET audio_alert_play_key_press(void);

/**
 * @brief 初始化音频提示音生成器
 * @return OPERATE_RET
 */
OPERATE_RET audio_alert_generator_init(void);

/**
 * @brief 测试音频播放功能
 * @return OPERATE_RET
 */
OPERATE_RET audio_alert_test_playback(void);

#ifdef __cplusplus
}
#endif

#endif /* AUDIO_ALERT_GENERATOR_H */
