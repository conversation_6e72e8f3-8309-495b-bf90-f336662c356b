/**
 * @file chinese_display_test.h
 * @brief Chinese display functionality test header
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef CHINESE_DISPLAY_TEST_H
#define CHINESE_DISPLAY_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 测试中文显示功能
 */
void chinese_display_test(void);

/**
 * @brief 测试中文文本样式
 */
void chinese_style_test(void);

/**
 * @brief 测试UTF-8编码处理
 */
void utf8_encoding_test(void);

/**
 * @brief 运行所有中文显示测试
 */
void run_all_chinese_tests(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*CHINESE_DISPLAY_TEST_H*/
