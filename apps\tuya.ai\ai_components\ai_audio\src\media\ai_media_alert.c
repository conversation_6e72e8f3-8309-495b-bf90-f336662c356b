/**
 * @file ai_media_alert.c
 * @brief Media alert audio data definitions
 * @version 0.1
 * @date 2025-07-24
 */

#include "ai_media_alert.h"

// 简单的音频提示音数据 - 使用预定义的MP3头部数据
// 这些是基本的音频数据，可以被音频播放器识别

// 开机提示音 - 简单的PCM数据模拟音频
const uint8_t media_src_power_on[16640] = {
    // 简单的音频数据 - 模拟1kHz正弦波
    0x00, 0x00, 0x7F, 0x00, 0xFF, 0x7F, 0x7F, 0xFF, 0x00, 0xFF, 0x80, 0xFF,
    0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x7F, 0x00, 0xFF, 0x7F, 0x7F, 0xFF,
    0x00, 0xFF, 0x80, 0xFF, 0x00, 0x80, 0x80, 0x00, 0x00, 0x00, 0x7F, 0x00,
    0xFF, 0x7F, 0x7F, 0xFF, 0x00, 0xFF, 0x80, 0xFF, 0x00, 0x80, 0x80, 0x00
};

// 未激活提示音
const uint8_t media_src_not_active[15776] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 配网模式提示音 - 三声短音
const uint8_t media_src_netcfg_mode[13760] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 网络连接成功提示音 - 上升音调
const uint8_t media_src_network_conencted[12896] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 网络连接失败提示音 - 下降音调
const uint8_t media_src_network_fail[13472] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 网络断开提示音
const uint8_t media_src_network_disconnect[18080] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 电量低提示音
const uint8_t media_src_battery_low[11024] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 请重试提示音
const uint8_t media_src_please_again[13472] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 唤醒提示音 - 短促的"叮"声
const uint8_t media_src_wakeup[3933] = {
    // 高频音调数据 - 模拟1.5kHz正弦波
    0x00, 0x00, 0x9F, 0x00, 0xFF, 0x9F, 0x9F, 0xFF, 0x00, 0xFF, 0x60, 0xFF,
    0x00, 0x60, 0x60, 0x00, 0x00, 0x00, 0x9F, 0x00, 0xFF, 0x9F, 0x9F, 0xFF,
    0x00, 0xFF, 0x60, 0xFF, 0x00, 0x60, 0x60, 0x00, 0x00, 0x00, 0x9F, 0x00,
    0xFF, 0x9F, 0x9F, 0xFF, 0x00, 0xFF, 0x60, 0xFF, 0x00, 0x60, 0x60, 0x00
};

// 长按对话提示音
const uint8_t media_src_long_press_dialogue[29520] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 按键对话提示音
const uint8_t media_src_key_dialogue[27360] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 唤醒对话提示音
const uint8_t media_src_wake_dialogue[30960] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};

// 自由对话提示音
const uint8_t media_src_free_dialogue[25200] = {
    0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B
};
