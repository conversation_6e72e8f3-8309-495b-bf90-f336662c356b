/**
 * @file display_fix.h
 * @brief Display optimization fixes for T5AI platform
 *
 * This header file contains optimizations and fixes for display refresh issues
 * including horizontal lines, flickering, and blurry display problems.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#ifndef __DISPLAY_FIX_H__
#define __DISPLAY_FIX_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

// Display refresh optimization settings
#define DISPLAY_REFRESH_RATE_HZ         30      // 30Hz refresh rate (33ms period)
#define DISPLAY_BUFFER_RATIO            5       // 1/5 of screen size for buffer
#define DISPLAY_FLUSH_BATCH_SIZE        4       // Batch multiple areas before flush

// Display timing optimization
#define DISPLAY_SPI_CLOCK_OPTIMIZE      1       // Enable SPI clock optimization
#define DISPLAY_VSYNC_ENABLE            1       // Enable vertical sync
#define DISPLAY_DOUBLE_BUFFER_ENABLE    1       // Enable double buffering

/***********************************************************
***********************typedef define***********************
***********************************************************/

typedef struct {
    uint32_t refresh_period_ms;     // Refresh period in milliseconds
    uint32_t buffer_size_ratio;     // Buffer size as ratio of screen size
    bool vsync_enabled;             // Vertical sync enable flag
    bool double_buffer_enabled;     // Double buffer enable flag
    uint32_t spi_clock_hz;          // SPI clock frequency
} display_config_t;

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief Initialize display optimization settings
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_init(void);

/**
 * @brief Apply display refresh rate optimization
 * 
 * @param refresh_hz Target refresh rate in Hz
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_set_refresh_rate(uint32_t refresh_hz);

/**
 * @brief Apply display buffer optimization
 * 
 * @param buffer_ratio Buffer size ratio (screen_size / buffer_ratio)
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_set_buffer_ratio(uint32_t buffer_ratio);

/**
 * @brief Enable/disable display vsync
 * 
 * @param enable Enable or disable vsync
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_set_vsync(bool enable);

/**
 * @brief Get current display configuration
 * 
 * @param config Pointer to store current configuration
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_get_config(display_config_t *config);

/**
 * @brief Apply all display optimizations
 * 
 * This function applies all recommended display optimizations
 * to fix refresh issues, horizontal lines, and flickering.
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET display_fix_apply_all_optimizations(void);

#ifdef __cplusplus
}
#endif

#endif /* __DISPLAY_FIX_H__ */
