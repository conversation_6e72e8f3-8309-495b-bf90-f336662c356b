/**
 * @file iot_comm_manager.h
 * @brief IoT通信管理模块 - 基于涂鸦IoT SDK
 * @version 1.0
 * @date 2024-12-23
 * 
 * 功能特性：
 * - IoT数据包管理（支持4种优先级）
 * - 网络状态管理和监控
 * - DP数据异步发送（5秒超时，3次重试）
 * - 统计信息管理（发送成功/失败统计）
 * - 队列管理（最大20个数据包）
 */

#ifndef __IOT_COMM_MANAGER_H__
#define __IOT_COMM_MANAGER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

// IoT通信配置
#define IOT_COMM_MAX_RETRY_COUNT        3           // 最大重试次数
#define IOT_COMM_RETRY_INTERVAL_MS      1000        // 重试间隔(毫秒)
#define IOT_COMM_TIMEOUT_MS             5000        // 超时时间(毫秒)
#define IOT_COMM_QUEUE_SIZE             20          // 队列大小
#define IOT_COMM_MAX_PAYLOAD_SIZE       1024        // 最大载荷大小

// 网络状态检查间隔
#define IOT_COMM_NETWORK_CHECK_INTERVAL 10000       // 网络状态检查间隔(毫秒)

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief IoT数据包优先级枚举
 */
typedef enum {
    IOT_PRIORITY_LOW = 0,       // 低优先级
    IOT_PRIORITY_NORMAL,        // 普通优先级
    IOT_PRIORITY_HIGH,          // 高优先级
    IOT_PRIORITY_URGENT         // 紧急优先级
} iot_priority_e;

/**
 * @brief IoT数据包结构体
 */
typedef struct {
    uint32_t id;                                    // 数据包ID
    iot_priority_e priority;                        // 优先级
    char payload[IOT_COMM_MAX_PAYLOAD_SIZE];        // 载荷数据
    uint32_t payload_size;                          // 载荷大小
    uint32_t create_time;                           // 创建时间戳
    uint32_t retry_count;                           // 重试次数
    bool is_sent;                                   // 是否已发送
} iot_data_packet_t;

/**
 * @brief IoT通信管理器状态结构体
 */
typedef struct {
    bool initialized;                               // 是否已初始化
    bool network_connected;                         // 网络连接状态
    uint32_t queue_head;                            // 队列头指针
    uint32_t queue_tail;                            // 队列尾指针
    uint32_t queue_count;                           // 队列中数据包数量
    uint32_t next_packet_id;                        // 下一个数据包ID
    uint32_t total_sent;                            // 总发送次数
    uint32_t total_success;                         // 总成功次数
    uint32_t total_failed;                          // 总失败次数
    uint32_t last_network_check;                    // 最后网络检查时间
} iot_comm_manager_t;

/**
 * @brief IoT通信统计信息结构体
 */
typedef struct {
    uint32_t total_packets;                         // 总数据包数
    uint32_t sent_packets;                          // 已发送数据包数
    uint32_t pending_packets;                       // 待发送数据包数
    uint32_t success_rate;                          // 成功率(百分比)
    uint32_t average_retry_count;                   // 平均重试次数
    uint32_t last_send_time;                        // 最后发送时间
} iot_comm_statistics_t;

/**
 * @brief 网络状态回调函数类型
 */
typedef void (*iot_network_status_callback_t)(bool connected);

/**
 * @brief 发送完成回调函数类型
 */
typedef void (*iot_send_complete_callback_t)(uint32_t packet_id, OPERATE_RET result);

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化IoT通信管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_manager_init(void);

/**
 * @brief 反初始化IoT通信管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_manager_deinit(void);

/**
 * @brief 设置网络状态
 * 
 * @param connected 网络连接状态
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_set_network_status(bool connected);

/**
 * @brief 获取网络状态
 * 
 * @return bool 网络连接状态
 */
bool iot_comm_get_network_status(void);

/**
 * @brief 发送DP数据（异步）
 * 
 * @param dp_json DP数据JSON字符串
 * @param priority 优先级
 * @return uint32_t 数据包ID（0表示失败）
 */
uint32_t iot_comm_send_dp_data(const char *dp_json, iot_priority_e priority);

/**
 * @brief 发送DP数据（同步）
 * 
 * @param dp_json DP数据JSON字符串
 * @param priority 优先级
 * @param timeout_ms 超时时间(毫秒)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_send_dp_data_sync(const char *dp_json, iot_priority_e priority, uint32_t timeout_ms);

/**
 * @brief 添加数据包到发送队列
 * 
 * @param dp_json DP数据JSON字符串
 * @param priority 优先级
 * @return uint32_t 数据包ID（0表示失败）
 */
uint32_t iot_comm_queue_packet(const char *dp_json, iot_priority_e priority);

/**
 * @brief 处理发送队列（定期调用）
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_process_queue(void);

/**
 * @brief 清空发送队列
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_clear_queue(void);

/**
 * @brief 获取管理器状态
 * 
 * @param manager 管理器状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_get_manager_status(iot_comm_manager_t *manager);

/**
 * @brief 获取统计信息
 * 
 * @param statistics 统计信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_get_statistics(iot_comm_statistics_t *statistics);

/**
 * @brief 重置统计信息
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_reset_statistics(void);

/**
 * @brief 设置网络状态回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_set_network_callback(iot_network_status_callback_t callback);

/**
 * @brief 设置发送完成回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_set_send_callback(iot_send_complete_callback_t callback);

/**
 * @brief 检查网络连接状态
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_check_network(void);

/**
 * @brief 重试失败的数据包
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_retry_failed_packets(void);

/**
 * @brief 获取队列中指定优先级的数据包数量
 * 
 * @param priority 优先级
 * @return uint32_t 数据包数量
 */
uint32_t iot_comm_get_queue_count_by_priority(iot_priority_e priority);

/**
 * @brief 获取优先级字符串
 * 
 * @param priority 优先级
 * @return const char* 优先级字符串
 */
const char* iot_comm_get_priority_string(iot_priority_e priority);

/**
 * @brief 定期维护任务（建议每秒调用一次）
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_comm_maintenance_task(void);

/**
 * @brief 运行所有测试用例
 * 
 * @return OPERATE_RET 测试结果
 */
OPERATE_RET iot_comm_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_COMM_MANAGER_H__ */
