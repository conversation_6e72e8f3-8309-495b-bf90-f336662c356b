/**
 * @file medication_manager.h
 * @brief Medication data management system
 *
 * This module manages medication data from file and provides
 * intelligent scheduling based on current date and time.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#ifndef __MEDICATION_MANAGER_H__
#define __MEDICATION_MANAGER_H__

#include "tuya_cloud_types.h"
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

#define MAX_MEDICATION_NAME_LEN     32
#define MAX_DOSAGE_LEN             16
#define MAX_NOTE_LEN               64
#define MAX_TIME_SLOTS             6
#define MAX_MEDICATIONS            50

/***********************************************************
***********************typedef define***********************
***********************************************************/

typedef struct {
    int hour;
    int minute;
} medication_time_t;

typedef struct {
    medication_time_t start_time;
    medication_time_t end_time;
} medication_time_slot_t;

typedef struct {
    char name[MAX_MEDICATION_NAME_LEN];
    struct tm start_date;
    struct tm end_date;
    medication_time_slot_t time_slots[MAX_TIME_SLOTS];
    int time_slot_count;
    char dosage[MAX_DOSAGE_LEN];
    char note[MAX_NOTE_LEN];
    int box_number;  // 药盒编号 (1-6)
    bool is_taken_today;
    bool is_active;
} medication_info_t;

typedef struct {
    medication_info_t medications[MAX_MEDICATIONS];
    int medication_count;
    struct tm current_date;
    bool data_loaded;
} medication_manager_t;

// External declaration for global medication manager
extern medication_manager_t sg_medication_manager;

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief Initialize medication manager
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_init(void);

/**
 * @brief Update current date and medication status
 *
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_update_date(void);

/**
 * @brief Load medication data from file
 *
 * @param file_path Path to medication data file
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_load_data(const char *file_path);

/**
 * @brief Save medication data to file
 *
 * @param file_path Path to save medication data file
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_save_data(const char *file_path);

/**
 * @brief Clear all medication data
 *
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_clear_all(void);

/**
 * @brief Add a single medication dynamically
 *
 * @param name Medication name
 * @param time_str Time slots string (e.g., "08:00-08:30 14:00-14:30")
 * @param dosage Dosage string
 * @param box_number Box number (1-6)
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_add_medication(const char *name, const char *time_str, const char *dosage, int box_number);

/**
 * @brief Remove a medication by name
 *
 * @param name Medication name to remove
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_remove_medication(const char *name);

/**
 * @brief Get current medication count
 *
 * @return int Number of medications
 */
int medication_manager_get_count(void);

/**
 * @brief Update medication data from external source
 *
 * @param medications Array of medication data
 * @param count Number of medications
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_update_data(medication_info_t *medications, int count);

/**
 * @brief Parse time slots string (internal function exposed for API)
 *
 * @param time_str Time slots string
 * @param med_info Medication info structure
 * @return OPERATE_RET Operation result
 */
OPERATE_RET __parse_time_slots(const char *time_str, medication_info_t *med_info);



// This function declaration was moved above

/**
 * @brief Get medications for today
 * 
 * @param today_meds Array to store today's medications
 * @param max_count Maximum number of medications to return
 * @return int Number of medications for today
 */
int medication_manager_get_today_medications(medication_info_t *today_meds, int max_count);

/**
 * @brief Get next medication time for today
 * 
 * @param next_med Pointer to store next medication info
 * @param next_time Pointer to store next medication time
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_get_next_medication(medication_info_t *next_med, medication_time_t *next_time);

/**
 * @brief Mark medication as taken for today
 * 
 * @param medication_name Name of the medication
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_mark_taken(const char *medication_name);

/**
 * @brief Check if it's time to take a medication
 * 
 * @param medication_name Name of the medication
 * @param current_time Current time
 * @return bool True if it's time to take the medication
 */
bool medication_manager_is_time_to_take(const char *medication_name, time_t current_time);

/**
 * @brief Get medication count for today
 * 
 * @return int Number of medications scheduled for today
 */
int medication_manager_get_today_count(void);

/**
 * @brief Reset daily medication status (call at midnight)
 * 
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_reset_daily_status(void);

/**
 * @brief Get medication manager instance
 *
 * @return medication_manager_t* Pointer to manager instance
 */
medication_manager_t* medication_manager_get_instance(void);

/**
 * @brief Format time slot for display
 *
 * @param time_slot Time slot to format
 * @param buffer Buffer to store formatted string
 * @param buffer_size Size of the buffer
 * @return OPERATE_RET Operation result
 */
OPERATE_RET medication_manager_format_time_slot(const medication_time_slot_t *time_slot,
                                               char *buffer, size_t buffer_size);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_MANAGER_H__ */
