/**
 * @file iot_data_handler.h
 * @brief IoT数据处理模块头文件
 * @version 1.0
 * @date 2025-01-24
 */

#ifndef __IOT_DATA_HANDLER_H__
#define __IOT_DATA_HANDLER_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/

#define IOT_MAX_DEVICE_NAME_LEN     32      // 设备名称最大长度
#define IOT_MAX_PROPERTY_NAME_LEN   32      // 属性名称最大长度
#define IOT_MAX_STRING_VALUE_LEN    128     // 字符串值最大长度
#define IOT_MAX_PROPERTIES          20      // 最大属性数量
#define IOT_MAX_COMMANDS            10      // 最大命令数量

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief IoT数据类型枚举
 */
typedef enum {
    IOT_DATA_TYPE_BOOL = 0,         // 布尔类型
    IOT_DATA_TYPE_INT,              // 整数类型
    IOT_DATA_TYPE_FLOAT,            // 浮点类型
    IOT_DATA_TYPE_STRING,           // 字符串类型
    IOT_DATA_TYPE_ENUM,             // 枚举类型
    IOT_DATA_TYPE_JSON              // JSON对象类型
} iot_data_type_e;

/**
 * @brief IoT属性访问权限枚举
 */
typedef enum {
    IOT_ACCESS_READ_ONLY = 0,       // 只读
    IOT_ACCESS_WRITE_ONLY,          // 只写
    IOT_ACCESS_READ_WRITE           // 读写
} iot_access_mode_e;

/**
 * @brief IoT数据值联合体
 */
typedef union {
    bool bool_val;                  // 布尔值
    int32_t int_val;                // 整数值
    float float_val;                // 浮点值
    char string_val[IOT_MAX_STRING_VALUE_LEN];  // 字符串值
} iot_data_value_t;

/**
 * @brief IoT属性定义结构体
 */
typedef struct {
    char name[IOT_MAX_PROPERTY_NAME_LEN];       // 属性名称
    iot_data_type_e type;                       // 数据类型
    iot_access_mode_e access;                   // 访问权限
    iot_data_value_t value;                     // 当前值
    iot_data_value_t min_value;                 // 最小值（数值类型）
    iot_data_value_t max_value;                 // 最大值（数值类型）
    bool changed;                               // 值是否已改变
    uint32_t last_update_time;                  // 最后更新时间
} iot_property_t;

/**
 * @brief IoT命令处理函数类型
 */
typedef OPERATE_RET (*iot_command_handler_t)(const char* command, const char* params, char* response, size_t response_size);

/**
 * @brief IoT命令定义结构体
 */
typedef struct {
    char name[IOT_MAX_PROPERTY_NAME_LEN];       // 命令名称
    iot_command_handler_t handler;              // 处理函数
    char description[64];                       // 命令描述
} iot_command_t;

/**
 * @brief IoT设备状态枚举
 */
typedef enum {
    IOT_DEVICE_STATE_OFFLINE = 0,   // 离线
    IOT_DEVICE_STATE_ONLINE,        // 在线
    IOT_DEVICE_STATE_ERROR,         // 错误
    IOT_DEVICE_STATE_UPDATING       // 更新中
} iot_device_state_e;

/**
 * @brief IoT设备信息结构体
 */
typedef struct {
    char device_id[IOT_MAX_DEVICE_NAME_LEN];    // 设备ID
    char device_name[IOT_MAX_DEVICE_NAME_LEN];  // 设备名称
    char firmware_version[16];                  // 固件版本
    iot_device_state_e state;                   // 设备状态
    uint32_t last_heartbeat_time;               // 最后心跳时间
    uint32_t uptime;                            // 运行时间
} iot_device_info_t;

/**
 * @brief IoT数据管理器结构体
 */
typedef struct {
    bool initialized;                           // 是否已初始化
    iot_device_info_t device_info;              // 设备信息
    iot_property_t properties[IOT_MAX_PROPERTIES];  // 属性列表
    iot_command_t commands[IOT_MAX_COMMANDS];   // 命令列表
    uint8_t property_count;                     // 属性数量
    uint8_t command_count;                      // 命令数量
    uint32_t last_sync_time;                    // 最后同步时间
} iot_data_manager_t;

/**
 * @brief IoT数据变化回调函数类型
 */
typedef void (*iot_property_change_callback_t)(const char* property_name, const iot_data_value_t* old_value, const iot_data_value_t* new_value);

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化IoT数据处理模块
 * 
 * @param device_id 设备ID
 * @param device_name 设备名称
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_handler_init(const char* device_id, const char* device_name);

/**
 * @brief 反初始化IoT数据处理模块
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_handler_deinit(void);

/**
 * @brief 注册IoT属性
 * 
 * @param name 属性名称
 * @param type 数据类型
 * @param access 访问权限
 * @param initial_value 初始值
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_register_property(const char* name, iot_data_type_e type, iot_access_mode_e access, const iot_data_value_t* initial_value);

/**
 * @brief 注册IoT命令
 * 
 * @param name 命令名称
 * @param handler 处理函数
 * @param description 命令描述
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_register_command(const char* name, iot_command_handler_t handler, const char* description);

/**
 * @brief 设置属性值
 * 
 * @param name 属性名称
 * @param value 属性值
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_set_property(const char* name, const iot_data_value_t* value);

/**
 * @brief 获取属性值
 * 
 * @param name 属性名称
 * @param value 输出属性值
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_property(const char* name, iot_data_value_t* value);

/**
 * @brief 处理云端下发的数据
 * 
 * @param json_data JSON格式的数据
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_handle_cloud_data(const char* json_data);

/**
 * @brief 生成上报到云端的数据
 * 
 * @param json_buffer 输出JSON缓冲区
 * @param buffer_size 缓冲区大小
 * @param only_changed 是否只上报变化的属性
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_generate_report_data(char* json_buffer, size_t buffer_size, bool only_changed);

/**
 * @brief 执行IoT命令
 * 
 * @param command_name 命令名称
 * @param params 命令参数（JSON格式）
 * @param response 命令响应（JSON格式）
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_execute_command(const char* command_name, const char* params, char* response, size_t response_size);

/**
 * @brief 更新设备状态
 * 
 * @param state 设备状态
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_update_device_state(iot_device_state_e state);

/**
 * @brief 发送心跳
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_send_heartbeat(void);

/**
 * @brief 获取设备信息
 * 
 * @param device_info 输出设备信息
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_device_info(iot_device_info_t* device_info);

/**
 * @brief 设置属性变化回调函数
 * 
 * @param callback 回调函数
 */
void iot_data_set_property_change_callback(iot_property_change_callback_t callback);

/**
 * @brief 同步所有属性到云端
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_sync_all_properties(void);

/**
 * @brief 获取属性统计信息
 * 
 * @param total_count 总属性数量
 * @param changed_count 已变化属性数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_property_statistics(uint8_t* total_count, uint8_t* changed_count);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_DATA_HANDLER_H__ */
