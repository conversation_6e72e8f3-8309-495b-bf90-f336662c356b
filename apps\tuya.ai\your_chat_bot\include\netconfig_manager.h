/**
 * @file netconfig_manager.h
 * @brief 配网状态管理器头文件
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef NETCONFIG_MANAGER_H
#define NETCONFIG_MANAGER_H

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 配网状态枚举
 */
typedef enum {
    NETCONFIG_STATE_IDLE = 0,       // 空闲状态
    NETCONFIG_STATE_STARTING,       // 启动中
    NETCONFIG_STATE_IN_PROGRESS,    // 配网进行中
    NETCONFIG_STATE_SUCCESS,        // 配网成功
    NETCONFIG_STATE_FAILED,         // 配网失败
    NETCONFIG_STATE_TIMEOUT,        // 配网超时
    NETCONFIG_STATE_STOPPED         // 已停止
} netconfig_state_e;

/**
 * @brief 配网事件枚举
 */
typedef enum {
    NETCONFIG_EVENT_STARTED = 0,    // 配网开始
    NETCONFIG_EVENT_SUCCESS,        // 配网成功
    NETCONFIG_EVENT_FAILED,         // 配网失败
    NETCONFIG_EVENT_TIMEOUT,        // 配网超时
    NETCONFIG_EVENT_STOPPED         // 配网停止
} netconfig_event_e;

/**
 * @brief 配网统计信息
 */
typedef struct {
    uint32_t retry_count;           // 重试次数
    uint32_t duration_ms;           // 持续时间(毫秒)
    netconfig_state_e current_state; // 当前状态
} netconfig_stats_t;

/**
 * @brief 配网事件回调函数类型
 */
typedef void (*netconfig_event_callback_t)(netconfig_event_e event, void *data);

/**
 * @brief 初始化配网管理器
 * @param callback 事件回调函数
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_init(netconfig_event_callback_t callback);

/**
 * @brief 开始配网
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_start(void);

/**
 * @brief 配网成功
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_success(void);

/**
 * @brief 配网失败
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_failed(void);

/**
 * @brief 停止配网
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_stop(void);

/**
 * @brief 获取当前配网状态
 * @return netconfig_state_e 
 */
netconfig_state_e netconfig_manager_get_state(void);

/**
 * @brief 获取配网统计信息
 * @param stats 统计信息结构体
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_get_stats(netconfig_stats_t *stats);

/**
 * @brief 重置配网状态
 * @return OPERATE_RET 
 */
OPERATE_RET netconfig_manager_reset(void);

#ifdef __cplusplus
}
#endif

#endif /* NETCONFIG_MANAGER_H */
