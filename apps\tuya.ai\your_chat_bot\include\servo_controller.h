/**
 * @file servo_controller.h
 * @brief 舵机控制模块 - 智能药盒分药系统核心控制
 * @version 1.0
 * @date 2024-12-23
 * 
 * 功能特性：
 * - PWM舵机控制（6个舵机，对应6个药品）
 * - 角度精确控制（0-180度，1度精度）
 * - 分药动作序列（开启-等待-关闭）
 * - 舵机状态监控（位置、运行状态、错误检测）
 * - 安全保护机制（过载保护、位置限制、超时保护）
 * - 校准和复位功能（自动校准、手动复位）
 */

#ifndef __SERVO_CONTROLLER_H__
#define __SERVO_CONTROLLER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

// 舵机硬件配置
#define SERVO_MAX_COUNT                 6           // 最大舵机数量
#define SERVO_PWM_FREQUENCY             50          // PWM频率 (50Hz)
#define SERVO_PWM_PERIOD_US             20000       // PWM周期 (20ms)
#define SERVO_PWM_MIN_PULSE_US          500         // 最小脉宽 (0.5ms)
#define SERVO_PWM_MAX_PULSE_US          2500        // 最大脉宽 (2.5ms)
#define SERVO_PWM_CENTER_PULSE_US       1500        // 中心脉宽 (1.5ms)

// 舵机角度配置
#define SERVO_MIN_ANGLE                 0           // 最小角度
#define SERVO_MAX_ANGLE                 180         // 最大角度
#define SERVO_CENTER_ANGLE              90          // 中心角度
#define SERVO_DISPENSE_ANGLE            45          // 分药角度
#define SERVO_CLOSE_ANGLE               0           // 关闭角度

// 舵机动作时间配置
#define SERVO_MOVE_DELAY_MS             500         // 舵机移动延时
#define SERVO_DISPENSE_DURATION_MS      2000        // 分药持续时间
#define SERVO_SETTLE_TIME_MS            100         // 稳定时间
#define SERVO_TIMEOUT_MS                5000        // 操作超时时间

// PWM引脚定义 (基于开发板硬件资源)
#define SERVO_PWM_PIN_1                 TUYA_GPIO_NUM_6     // P06 - pwm0:0
#define SERVO_PWM_PIN_2                 TUYA_GPIO_NUM_7     // P07 - pwm0:1
#define SERVO_PWM_PIN_3                 TUYA_GPIO_NUM_8     // P08 - pwm0:2
#define SERVO_PWM_PIN_4                 TUYA_GPIO_NUM_9     // P09 - pwm0:3 (扩展)
#define SERVO_PWM_PIN_5                 TUYA_GPIO_NUM_10    // P10 - pwm0:4 (扩展)
#define SERVO_PWM_PIN_6                 TUYA_GPIO_NUM_11    // P11 - pwm0:5 (扩展)

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 舵机ID枚举
 */
typedef enum {
    SERVO_ID_1 = 0,                 // 舵机1 (药品1)
    SERVO_ID_2 = 1,                 // 舵机2 (药品2)
    SERVO_ID_3 = 2,                 // 舵机3 (药品3)
    SERVO_ID_4 = 3,                 // 舵机4 (药品4)
    SERVO_ID_5 = 4,                 // 舵机5 (药品5)
    SERVO_ID_6 = 5                  // 舵机6 (药品6)
} servo_id_e;

/**
 * @brief 舵机状态枚举
 */
typedef enum {
    SERVO_STATE_IDLE = 0,           // 空闲状态
    SERVO_STATE_MOVING,             // 移动中
    SERVO_STATE_DISPENSING,         // 分药中
    SERVO_STATE_ERROR               // 错误状态
} servo_state_e;

/**
 * @brief 舵机动作类型枚举
 */
typedef enum {
    SERVO_ACTION_NONE = 0,          // 无动作
    SERVO_ACTION_MOVE_TO_ANGLE,     // 移动到指定角度
    SERVO_ACTION_DISPENSE,          // 执行分药动作
    SERVO_ACTION_RESET,             // 复位到初始位置
    SERVO_ACTION_CALIBRATE          // 校准动作
} servo_action_e;

/**
 * @brief 舵机配置结构体
 */
typedef struct {
    servo_id_e id;                  // 舵机ID
    uint8_t pwm_channel;            // PWM通道
    uint16_t min_pulse_us;          // 最小脉宽(微秒)
    uint16_t max_pulse_us;          // 最大脉宽(微秒)
    uint8_t min_angle;              // 最小角度
    uint8_t max_angle;              // 最大角度
    uint8_t dispense_angle;         // 分药角度
    uint8_t close_angle;            // 关闭角度
    bool enabled;                   // 是否启用
} servo_config_t;

/**
 * @brief 舵机状态结构体
 */
typedef struct {
    servo_id_e id;                  // 舵机ID
    servo_state_e state;            // 当前状态
    uint8_t current_angle;          // 当前角度
    uint8_t target_angle;           // 目标角度
    uint32_t last_move_time;        // 最后移动时间
    uint32_t total_moves;           // 总移动次数
    uint32_t total_dispenses;       // 总分药次数
    uint32_t error_count;           // 错误次数
    bool is_calibrated;             // 是否已校准
} servo_status_t;

/**
 * @brief 舵机控制器管理结构体
 */
typedef struct {
    bool initialized;               // 是否已初始化
    servo_config_t configs[SERVO_MAX_COUNT];    // 舵机配置数组
    servo_status_t statuses[SERVO_MAX_COUNT];   // 舵机状态数组
    uint8_t servo_count;            // 舵机数量
    uint32_t total_operations;      // 总操作次数
    uint32_t total_errors;          // 总错误次数
    uint32_t last_maintenance_time; // 最后维护时间
} servo_controller_manager_t;

/**
 * @brief 舵机统计信息结构体
 */
typedef struct {
    uint32_t total_servos;          // 总舵机数
    uint32_t active_servos;         // 活跃舵机数
    uint32_t total_moves;           // 总移动次数
    uint32_t total_dispenses;       // 总分药次数
    uint32_t total_errors;          // 总错误次数
    uint32_t success_rate;          // 成功率(百分比)
    uint32_t last_operation_time;   // 最后操作时间
} servo_statistics_t;

/**
 * @brief 舵机动作完成回调函数类型
 */
typedef void (*servo_action_callback_t)(servo_id_e servo_id, servo_action_e action, bool success);

/**
 * @brief 舵机错误回调函数类型
 */
typedef void (*servo_error_callback_t)(servo_id_e servo_id, const char *error_msg);

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化舵机控制器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_init(void);

/**
 * @brief 反初始化舵机控制器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_deinit(void);

/**
 * @brief 配置舵机参数
 * 
 * @param servo_id 舵机ID
 * @param config 舵机配置结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_config(servo_id_e servo_id, const servo_config_t *config);

/**
 * @brief 启用/禁用舵机
 * 
 * @param servo_id 舵机ID
 * @param enabled 是否启用
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_set_enabled(servo_id_e servo_id, bool enabled);

/**
 * @brief 设置舵机角度
 * 
 * @param servo_id 舵机ID
 * @param angle 目标角度 (0-180度)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_set_angle(servo_id_e servo_id, uint8_t angle);

/**
 * @brief 获取舵机当前角度
 * 
 * @param servo_id 舵机ID
 * @return uint8_t 当前角度 (255表示错误)
 */
uint8_t servo_controller_get_angle(servo_id_e servo_id);

/**
 * @brief 执行分药动作
 * 
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_dispense(servo_id_e servo_id);

/**
 * @brief 复位舵机到初始位置
 * 
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_reset(servo_id_e servo_id);

/**
 * @brief 复位所有舵机
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_reset_all(void);

/**
 * @brief 校准舵机
 * 
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_calibrate(servo_id_e servo_id);

/**
 * @brief 校准所有舵机
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_calibrate_all(void);

/**
 * @brief 获取舵机状态
 * 
 * @param servo_id 舵机ID
 * @param status 状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_get_status(servo_id_e servo_id, servo_status_t *status);

/**
 * @brief 获取所有舵机状态
 * 
 * @param statuses 状态数组
 * @param max_count 最大数量
 * @param actual_count 实际获取数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_get_all_status(servo_status_t *statuses, 
                                           uint8_t max_count, 
                                           uint8_t *actual_count);

/**
 * @brief 获取管理器状态
 * 
 * @param manager 管理器状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_get_manager_status(servo_controller_manager_t *manager);

/**
 * @brief 获取统计信息
 * 
 * @param statistics 统计信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_get_statistics(servo_statistics_t *statistics);

/**
 * @brief 设置动作完成回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_set_action_callback(servo_action_callback_t callback);

/**
 * @brief 设置错误回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_set_error_callback(servo_error_callback_t callback);

/**
 * @brief 角度转换为PWM脉宽
 * 
 * @param servo_id 舵机ID
 * @param angle 角度
 * @return uint16_t PWM脉宽(微秒)
 */
uint16_t servo_controller_angle_to_pulse(servo_id_e servo_id, uint8_t angle);

/**
 * @brief PWM脉宽转换为角度
 * 
 * @param servo_id 舵机ID
 * @param pulse_us PWM脉宽(微秒)
 * @return uint8_t 角度
 */
uint8_t servo_controller_pulse_to_angle(servo_id_e servo_id, uint16_t pulse_us);

/**
 * @brief 获取舵机状态字符串
 * 
 * @param state 舵机状态
 * @return const char* 状态字符串
 */
const char* servo_controller_get_state_string(servo_state_e state);

/**
 * @brief 获取动作类型字符串
 * 
 * @param action 动作类型
 * @return const char* 动作字符串
 */
const char* servo_controller_get_action_string(servo_action_e action);

/**
 * @brief 维护任务（定期调用）
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_maintenance(void);

/**
 * @brief 测试舵机功能
 * 
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_controller_test_servo(servo_id_e servo_id);

/**
 * @brief 运行所有测试用例
 * 
 * @return OPERATE_RET 测试结果
 */
OPERATE_RET servo_controller_run_tests(void);

/**
 * @brief 药品调度管理器接口 - 分药动作
 * 
 * @param servo_id 舵机ID (对应medication_servo_id_e)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET servo_dispense_medication(uint8_t servo_id);

#ifdef __cplusplus
}
#endif

#endif /* __SERVO_CONTROLLER_H__ */
