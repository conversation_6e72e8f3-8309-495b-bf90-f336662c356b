# 药箱设备UI系统设计文档

## 1. 系统概述

药箱设备UI系统基于现有的LVGL聊天机器人UI框架，扩展实现药物管理功能。系统包含三个主要页面：主菜单页面、聊天页面、服药页面。

## 2. 页面架构设计

### 2.1 主菜单页面 (Main Menu)
- **功能**: 设备启动后的首页，提供功能入口和药物概览
- **布局**:
  - 顶部状态栏：时间显示、网络状态
  - 中部内容区：
    - 聊天图标按钮 (左侧)
    - 服药图标按钮 (右侧)
    - 今日待服药物预览列表
  - 底部信息栏：设备状态信息

### 2.2 聊天页面 (Chat Page)
- **功能**: AI对话交互，复用现有聊天机器人功能
- **特性**:
  - 语音+文字双重呈现
  - emoji表情显示
  - 实时对话流

### 2.3 服药页面 (Medicine Page)
- **功能**: 药物信息展示和服药操作
- **布局**:
  - 顶部状态栏：时间、返回按钮
  - 中部药物信息区：
    - 药物名称
    - 服用时间
    - 剂量信息
    - 服用状态
  - 底部操作区：
    - 前一页按钮 "←"
    - 服药按钮 (红色)
    - 完成按钮 (服药后显示)
    - 后一页按钮 "→"

## 3. 数据结构设计

### 3.1 药物信息结构
```c
typedef struct {
    char name[64];              // 药物名称
    char dosage[32];            // 剂量信息
    uint32_t schedule_time;     // 计划服用时间(时间戳)
    uint32_t actual_time;       // 实际服用时间(时间戳)
    uint8_t status;             // 服用状态
    uint8_t is_taken;           // 是否已服用
} medicine_info_t;

typedef enum {
    MEDICINE_STATUS_PENDING = 0,    // 待服用
    MEDICINE_STATUS_TAKEN = 1,      // 已服用
    MEDICINE_STATUS_OVERDUE = 2,    // 逾期未服
    MEDICINE_STATUS_SKIPPED = 3     // 跳过
} medicine_status_e;
```

### 3.2 页面状态管理
```c
typedef enum {
    UI_PAGE_MAIN_MENU = 0,
    UI_PAGE_CHAT = 1,
    UI_PAGE_MEDICINE = 2
} ui_page_e;

typedef struct {
    ui_page_e current_page;
    uint8_t medicine_page_index;    // 当前药物页面索引
    uint8_t total_medicines;        // 今日药物总数
} ui_state_t;
```

## 4. 颜色状态设计

### 4.1 药物状态颜色
- **绿色 (0x00FF00)**: 待服药物，未逾期
- **灰色 (0x808080)**: 已服用药物
- **红色 (0xFF0000)**: 逾期未服药物

### 4.2 UI主题色彩
- **主背景色**: 白色 (0xFFFFFF)
- **按钮主色**: 蓝色 (0x007AFF)
- **服药按钮**: 红色 (0xFF3B30)
- **完成按钮**: 绿色 (0x34C759)

## 5. 交互流程设计

### 5.1 主菜单交互
1. 设备启动 → 显示主菜单
2. 点击聊天图标 → 跳转聊天页面
3. 点击服药图标 → 跳转服药页面
4. 显示今日药物预览列表

### 5.2 服药页面交互
1. 进入服药页面 → 显示第一个待服药物
2. 点击"←"/"→" → 切换药物页面
3. 点击"服药"按钮 → 显示"完成"按钮
4. 点击"完成"按钮 → 更新药物状态，隐藏完成按钮

### 5.3 语音提醒逻辑
1. 定时检查当前时间
2. 在服药时间段内 → 播放提醒语音
3. 药物逾期未服 → 播放警报语音

## 6. 文件结构规划

```
apps/tuya.ai/medicine_box/
├── CMakeLists.txt
├── include/
│   ├── medicine_manager.h      // 药物管理
│   ├── ui_medicine.h          // 药物UI
│   ├── ui_main_menu.h         // 主菜单UI
│   └── voice_reminder.h       // 语音提醒
├── src/
│   ├── medicine_manager.c     // 药物数据管理
│   ├── ui_medicine.c          // 服药页面实现
│   ├── ui_main_menu.c         // 主菜单页面实现
│   ├── voice_reminder.c       // 语音提醒实现
│   └── app_medicine_box.c     // 主应用入口
├── assets/
│   ├── lang_config.h          // 中文文本定义
│   └── icons/                 // 图标资源
└── config/
    └── medicine_config.h      // 配置参数
```

## 7. 技术要点

### 7.1 时间管理
- 使用 `tal_time_get_local_time_custom()` 获取本地时间
- 实现时间格式化显示函数
- 定时器检查服药时间

### 7.2 状态持久化
- 使用KV存储保存药物状态
- 设备重启后恢复药物信息

### 7.3 语音集成
- 复用现有AI音频模块
- 实现药物提醒语音播放

## 8. 开发优先级

1. **高优先级**: 主菜单页面、药物数据结构
2. **中优先级**: 服药页面、状态管理
3. **低优先级**: 语音提醒、UI美化