/**
 * @file chatbot_language_manager.h
 * @brief 聊天机器人语言管理器头文件
 * @version 1.0
 * @date 2025-01-24
 */

#ifndef __CHATBOT_LANGUAGE_MANAGER_H__
#define __CHATBOT_LANGUAGE_MANAGER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
***********************macro define************************
***********************************************************/

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 支持的语言类型
 */
typedef enum {
    CHATBOT_LANG_ENGLISH = 0,   // 英语
    CHATBOT_LANG_CHINESE,       // 中文
    CHATBOT_LANG_SPANISH,       // 西班牙语
    CHATBOT_LANG_FRENCH,        // 法语
    CHATBOT_LANG_MAX
} chatbot_language_e;

/**
 * @brief 聊天机器人状态
 */
typedef enum {
    CHATBOT_STATUS_READY = 0,   // 准备就绪
    CHATBOT_STATUS_LISTENING,   // 正在听取
    CHATBOT_STATUS_PROCESSING,  // 正在处理
    CHATBOT_STATUS_SPEAKING,    // 正在说话
    CHATBOT_STATUS_ERROR,       // 错误状态
    CHATBOT_STATUS_OFFLINE,     // 离线状态
    CHATBOT_STATUS_MAX
} chatbot_status_e;

/**
 * @brief 语言管理器统计信息
 */
typedef struct {
    uint32_t total_responses;       // 总响应次数
    chatbot_language_e current_language; // 当前语言
    uint32_t last_response_time;    // 最后响应时间
    uint32_t uptime;                // 运行时间
} chatbot_language_stats_t;

/**
 * @brief 聊天机器人语言管理器结构体
 */
typedef struct {
    bool initialized;               // 是否已初始化
    chatbot_language_e current_language; // 当前语言
    uint32_t response_count;        // 响应计数
    uint32_t last_response_time;    // 最后响应时间
} chatbot_language_manager_t;

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化聊天机器人语言管理器
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_manager_init(void);

/**
 * @brief 处理用户输入并生成响应
 * @param user_input 用户输入文本
 * @param response 响应缓冲区
 * @param response_size 响应缓冲区大小
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_process_input(const char* user_input, char* response, size_t response_size);

/**
 * @brief 设置语言
 * @param language 语言类型
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_set_language(chatbot_language_e language);

/**
 * @brief 获取当前语言
 * @return chatbot_language_e 当前语言
 */
chatbot_language_e chatbot_language_get_current_language(void);

/**
 * @brief 获取预设问候语
 * @param greeting 问候语缓冲区
 * @param greeting_size 缓冲区大小
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_get_greeting(char* greeting, size_t greeting_size);

/**
 * @brief 获取系统状态消息
 * @param status 状态类型
 * @param message 消息缓冲区
 * @param message_size 缓冲区大小
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_get_status_message(chatbot_status_e status, char* message, size_t message_size);

/**
 * @brief 获取统计信息
 * @param stats 统计信息结构体
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_get_statistics(chatbot_language_stats_t* stats);

/**
 * @brief 重置统计信息
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_reset_statistics(void);

/**
 * @brief 反初始化聊天机器人语言管理器
 * @return OPERATE_RET
 */
OPERATE_RET chatbot_language_manager_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __CHATBOT_LANGUAGE_MANAGER_H__ */
