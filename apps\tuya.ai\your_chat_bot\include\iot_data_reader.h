/**
 * @file iot_data_reader.h
 * @brief IoT数据读取接口 - 基于tuya_main.c技术文档
 * @version 1.0
 * @date 2025-07-22
 */

#ifndef __IOT_DATA_READER_H__
#define __IOT_DATA_READER_H__

#include "tuya_cloud_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

#define MAX_MEDICATION_SLOTS    6
#define MEDICATION_NAME_MAX_LEN 32

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 药品配置结构体 (基于技术文档)
 */
typedef struct {
    char medication_name[MEDICATION_NAME_MAX_LEN];  // 药品名称
    uint32_t medication_id;                         // 药品唯一ID
    uint8_t dosage_per_time;                       // 每次剂量 (1-10)
    uint8_t times_per_day;                         // 每日次数 (1-6)
    uint8_t duration_days;                         // 持续天数 (1-30)
    bool is_active;                                // 是否激活
    uint32_t created_time;                         // 创建时间戳
    uint32_t last_modified;                        // 最后修改时间
} iot_medication_config_t;

/**
 * @brief IoT统计数据结构体 (基于技术文档)
 */
typedef struct {
    uint32_t total_dispenses;          // 总分药次数
    uint32_t successful_dispenses;     // 成功分药次数
    uint32_t failed_dispenses;         // 失败分药次数
    uint32_t total_uptime_seconds;     // 总运行时间(秒)
    uint32_t last_dispense_time;       // 最后分药时间
    uint32_t system_start_time;        // 系统启动时间
    uint32_t network_reconnects;       // 网络重连次数
    uint32_t error_count;              // 错误计数
    float success_rate;                // 成功率 (0.0-1.0)
    uint32_t daily_dispense_count;     // 当日分药次数
} iot_statistics_t;

/**
 * @brief 舵机状态结构体 (基于技术文档)
 */
typedef struct {
    uint8_t servo_index;               // 舵机索引 (0-5)
    uint8_t gpio_pin;                  // GPIO引脚号
    uint16_t current_angle;            // 当前角度 (0-180)
    uint16_t target_angle;             // 目标角度
    uint32_t last_operation_time;      // 最后操作时间
    bool is_active;                    // 是否激活
    uint32_t operation_count;          // 操作次数统计
} iot_servo_control_t;

/**
 * @brief 系统健康状态结构体 (基于技术文档)
 */
typedef struct {
    bool flash_healthy;                // Flash存储健康
    bool network_healthy;              // 网络连接健康
    bool servo_healthy[MAX_MEDICATION_SLOTS]; // 舵机健康状态
    bool memory_healthy;               // 内存健康
    bool timer_healthy;                // 定时器健康
    uint32_t last_health_check;        // 最后健康检查时间
    uint32_t health_check_count;       // 健康检查次数
    uint32_t health_issues_detected;   // 检测到的健康问题数
} iot_system_health_t;

/***********************************************************
********************function declaration********************
***********************************************************/

/**
 * @brief 初始化IoT数据读取器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_reader_init(void);

/**
 * @brief 获取药品配置数据
 * 
 * @param configs 药品配置数组
 * @param max_count 最大数量
 * @return int 实际获取的数量
 */
int iot_data_get_medication_configs(iot_medication_config_t *configs, int max_count);

/**
 * @brief 获取IoT统计数据
 * 
 * @param stats 统计数据结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_statistics(iot_statistics_t *stats);

/**
 * @brief 获取舵机状态数据
 * 
 * @param servos 舵机状态数组
 * @param max_count 最大数量
 * @return int 实际获取的数量
 */
int iot_data_get_servo_states(iot_servo_control_t *servos, int max_count);

/**
 * @brief 获取系统健康状态
 * 
 * @param health 健康状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_system_health(iot_system_health_t *health);

/**
 * @brief 获取设备运行时间
 * 
 * @return uint32_t 运行时间(秒)
 */
uint32_t iot_data_get_uptime(void);

/**
 * @brief 获取网络状态
 * 
 * @return bool true:连接, false:断开
 */
bool iot_data_get_network_status(void);

/**
 * @brief 获取内存使用情况
 * 
 * @param total_heap 总堆内存(字节)
 * @param free_heap 可用堆内存(字节)
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET iot_data_get_memory_usage(uint32_t *total_heap, uint32_t *free_heap);

#ifdef __cplusplus
}
#endif

#endif /* __IOT_DATA_READER_H__ */
