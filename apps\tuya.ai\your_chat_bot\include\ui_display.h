/**
 * @file ui_display.h
 * @brief Head<PERSON> file for UI Display
 *
 * This header file defines the interface for the UI display module, including
 * initialization functions and data structures for managing fonts, emojis, and
 * other display elements. It provides the necessary declarations for integrating
 * display functionality into the chatbot application.
 *
 * @copyright Copyright (c) 2021-2025 Tuya Inc. All Rights Reserved.
 *
 */

#ifndef __UI_DISPLAY_H__
#define __UI_DISPLAY_H__

#include "tuya_cloud_types.h"

#include "lang_config.h"

#include "lvgl.h"

#include "app_display.h"

#include "font_chinese_16.h"
#include "chinese_text_config.h"

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
************************macro define************************
***********************************************************/
#define EMO_ICON_MAX_NUM 7

/***********************************************************
***********************typedef define***********************
***********************************************************/
typedef struct {
    char emo_text[32];
    char *emo_icon;
} UI_EMOJI_LIST_T;

typedef struct {
    lv_font_t *text;
    lv_font_t *icon;
    const lv_font_t *chinese;  // 中文字体支持

    const lv_font_t *emoji;
    UI_EMOJI_LIST_T *emoji_list;
} UI_FONT_T;

typedef enum {
    UI_PAGE_MAIN_MENU = 0,
    UI_PAGE_CHAT_BOT,
    UI_PAGE_MEDICATION,
    UI_PAGE_MAX
} ui_page_type_e;

typedef int (*ui_page_init_cb_t)(UI_FONT_T *ui_font);
typedef void (*ui_page_show_cb_t)(void);
typedef void (*ui_page_hide_cb_t)(void);

typedef struct {
    ui_page_type_e page_type;
    const char *page_name;
    ui_page_init_cb_t init_cb;
    ui_page_show_cb_t show_cb;
    ui_page_hide_cb_t hide_cb;
    lv_obj_t *page_obj;
} ui_page_info_t;

/***********************************************************
********************function declaration********************
***********************************************************/

int ui_init(UI_FONT_T *ui_font);

// Page navigation functions
void ui_page_switch(ui_page_type_e page_type);
ui_page_type_e ui_page_get_current(void);

// Main menu page functions
int ui_main_menu_init(UI_FONT_T *ui_font);
void ui_main_menu_show(void);
void ui_main_menu_hide(void);
void ui_main_menu_set_network_status(bool connected);
void ui_main_menu_update_time(time_t *current_time);

// Chat bot page functions (existing)
int ui_chat_bot_init(UI_FONT_T *ui_font);
void ui_chat_bot_show(void);
void ui_chat_bot_hide(void);
void ui_set_user_msg(const char *text);
void ui_set_assistant_msg(const char *text);
void ui_set_system_msg(const char *text);
void ui_set_emotion(const char *emotion);
void ui_set_status(const char *status);
void ui_set_notification(const char *notification);
void ui_set_network(char *wifi_icon);
void ui_set_chat_mode(const char *chat_mode);
void ui_set_status_bar_pad(int32_t value);

// Medication page functions
int ui_medication_init(UI_FONT_T *ui_font);
void ui_medication_show(void);
void ui_medication_hide(void);
void ui_medication_add_reminder(const char *medicine_name, const char *time, const char *dosage);
void ui_medication_set_next_reminder(const char *medicine_name, const char *time);
void ui_medication_mark_taken(const char *medicine_name);
void ui_medication_refresh(void);

// Main menu refresh function
void ui_main_menu_refresh(void);

#if defined(ENABLE_GUI_STREAM_AI_TEXT) && (ENABLE_GUI_STREAM_AI_TEXT == 1)
void ui_set_assistant_msg_stream_start(void);

void ui_set_assistant_msg_stream_data(const char *text);

void ui_set_assistant_msg_stream_end(void);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __UI_DISPLAY_H__ */
