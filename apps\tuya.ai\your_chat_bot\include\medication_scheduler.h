/**
 * @file medication_scheduler.h
 * @brief 药品调度管理模块 - 智能药盒核心调度系统
 * @version 1.0
 * @date 2024-12-23
 * 
 * 功能特性：
 * - 药品调度配置管理（最多6个药品）
 * - 时间段管理（每个药品最多3个时间段）
 * - 分药逻辑控制（基于频率和时间段）
 * - 舵机集成控制（6个舵机对应6个药品）
 * - 每日重置机制（0点重置分药标志）
 * - 错误日志管理（最多10条错误记录）
 */

#ifndef __MEDICATION_SCHEDULER_H__
#define __MEDICATION_SCHEDULER_H__

#include "tuya_cloud_types.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/***********************************************************
*************************macro define***********************
***********************************************************/

// 调度系统配置
#define MEDICATION_MAX_SCHEDULES        6           // 最大药品调度数
#define MEDICATION_MAX_TIMESLOTS        3           // 每个药品最大时间段数
#define MEDICATION_MAX_WEEKDAYS         2           // 每周最大天数配置
#define MEDICATION_MAX_ERRORS           10          // 最大错误记录数
#define MEDICATION_ID_MAX_LEN           16          // 药品ID最大长度
#define MEDICATION_NAME_MAX_LEN         32          // 药品名称最大长度
#define MEDICATION_LABEL_MAX_LEN        16          // 时间段标签最大长度
#define MEDICATION_ERROR_MSG_MAX_LEN    64          // 错误信息最大长度

// 检查间隔配置
#define MEDICATION_CHECK_INTERVAL_SEC   60          // 调度检查间隔(秒)
#define MEDICATION_DAILY_RESET_HOUR     0           // 每日重置时间(小时)

/***********************************************************
***********************typedef define***********************
***********************************************************/

/**
 * @brief 药品服用频率枚举
 */
typedef enum {
    MEDICATION_FREQ_DAILY_ONCE = 1,     // 每日一次
    MEDICATION_FREQ_DAILY_TWICE = 2,    // 每日两次
    MEDICATION_FREQ_DAILY_THRICE = 3,   // 每日三次
    MEDICATION_FREQ_EVERY_OTHER_DAY = 4,// 隔日一次
    MEDICATION_FREQ_WEEKLY_ONCE = 5,    // 每周一次
    MEDICATION_FREQ_WEEKLY_TWICE = 6    // 每周两次
} medication_frequency_e;

/**
 * @brief 星期枚举
 */
typedef enum {
    MEDICATION_WEEKDAY_SUNDAY = 0,      // 星期日
    MEDICATION_WEEKDAY_MONDAY = 1,      // 星期一
    MEDICATION_WEEKDAY_TUESDAY = 2,     // 星期二
    MEDICATION_WEEKDAY_WEDNESDAY = 3,   // 星期三
    MEDICATION_WEEKDAY_THURSDAY = 4,    // 星期四
    MEDICATION_WEEKDAY_FRIDAY = 5,      // 星期五
    MEDICATION_WEEKDAY_SATURDAY = 6     // 星期六
} medication_weekday_e;

/**
 * @brief 舵机ID枚举
 */
typedef enum {
    MEDICATION_SERVO_1 = 0,             // 舵机1
    MEDICATION_SERVO_2 = 1,             // 舵机2
    MEDICATION_SERVO_3 = 2,             // 舵机3
    MEDICATION_SERVO_4 = 3,             // 舵机4
    MEDICATION_SERVO_5 = 4,             // 舵机5
    MEDICATION_SERVO_6 = 5              // 舵机6
} medication_servo_id_e;

/**
 * @brief 时间段结构体
 */
typedef struct {
    uint8_t start_hour;                                 // 开始小时
    uint8_t start_minute;                               // 开始分钟
    uint8_t end_hour;                                   // 结束小时
    uint8_t end_minute;                                 // 结束分钟
    char label[MEDICATION_LABEL_MAX_LEN];               // 时间段标签
    bool dispensed_today;                               // 今日是否已分药
} medication_timeslot_t;

/**
 * @brief 日期结构体
 */
typedef struct {
    uint16_t year;                                      // 年
    uint8_t month;                                      // 月
    uint8_t day;                                        // 日
} medication_date_t;

/**
 * @brief 药品调度结构体
 */
typedef struct {
    char id[MEDICATION_ID_MAX_LEN];                     // 药品ID
    char name[MEDICATION_NAME_MAX_LEN];                 // 药品名称
    medication_frequency_e frequency;                   // 服用频率
    medication_servo_id_e servo_id;                     // 对应舵机ID
    bool enabled;                                       // 是否启用
    medication_timeslot_t timeslots[MEDICATION_MAX_TIMESLOTS]; // 时间段数组
    uint8_t timeslot_count;                             // 时间段数量
    medication_weekday_e weekdays[MEDICATION_MAX_WEEKDAYS]; // 星期配置
    uint8_t weekday_count;                              // 星期数量
    medication_date_t start_date;                       // 开始日期
    uint32_t last_check_time;                           // 最后检查时间
} medication_schedule_t;

/**
 * @brief 药品错误记录结构体
 */
typedef struct {
    char med_id[MEDICATION_ID_MAX_LEN];                 // 药品ID
    char timeslot_label[MEDICATION_LABEL_MAX_LEN];      // 时间段标签
    char error_msg[MEDICATION_ERROR_MSG_MAX_LEN];       // 错误信息
    uint32_t timestamp;                                 // 时间戳
} medication_error_t;

/**
 * @brief 药品调度管理器结构体
 */
typedef struct {
    bool initialized;                                   // 是否已初始化
    medication_schedule_t schedules[MEDICATION_MAX_SCHEDULES]; // 调度数组
    uint8_t schedule_count;                             // 调度数量
    bool system_enabled;                                // 系统是否启用
    medication_error_t errors[MEDICATION_MAX_ERRORS];   // 错误记录数组
    uint8_t error_count;                                // 错误记录数量
    uint32_t last_daily_reset;                          // 最后每日重置时间
    uint32_t total_dispensed;                           // 总分药次数
    uint32_t total_missed;                              // 总错过次数
} medication_scheduler_manager_t;

/**
 * @brief 调度统计信息结构体
 */
typedef struct {
    uint32_t total_schedules;                           // 总调度数
    uint32_t active_schedules;                          // 活跃调度数
    uint32_t total_timeslots;                           // 总时间段数
    uint32_t dispensed_today;                           // 今日已分药次数
    uint32_t pending_today;                             // 今日待分药次数
    uint32_t error_count;                               // 错误数量
    uint32_t last_dispense_time;                        // 最后分药时间
} medication_scheduler_statistics_t;

/**
 * @brief 分药完成回调函数类型
 */
typedef void (*medication_dispense_callback_t)(const char *med_id, const char *med_name, bool success);

/**
 * @brief 调度错误回调函数类型
 */
typedef void (*medication_error_callback_t)(const char *med_id, const char *error_msg);

/***********************************************************
***********************function define**********************
***********************************************************/

/**
 * @brief 初始化药品调度管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_init(void);

/**
 * @brief 反初始化药品调度管理器
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_deinit(void);

/**
 * @brief 启用/禁用调度系统
 * 
 * @param enabled 是否启用
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_set_enabled(bool enabled);

/**
 * @brief 获取调度系统启用状态
 * 
 * @return bool 是否启用
 */
bool medication_scheduler_is_enabled(void);

/**
 * @brief 添加药品调度
 * 
 * @param id 药品ID（如果为NULL则自动生成）
 * @param name 药品名称
 * @param frequency 服用频率
 * @param servo_id 舵机ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_add_schedule(const char *id, const char *name,
                                             medication_frequency_e frequency,
                                             medication_servo_id_e servo_id);

/**
 * @brief 删除药品调度
 * 
 * @param med_id 药品ID
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_remove_schedule(const char *med_id);

/**
 * @brief 启用/禁用特定药品调度
 * 
 * @param med_id 药品ID
 * @param enabled 是否启用
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_set_schedule_enabled(const char *med_id, bool enabled);

/**
 * @brief 添加时间段到药品调度
 * 
 * @param med_id 药品ID
 * @param start_hour 开始小时
 * @param start_minute 开始分钟
 * @param end_hour 结束小时
 * @param end_minute 结束分钟
 * @param label 时间段标签（可选）
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_add_timeslot(const char *med_id,
                                             uint8_t start_hour, uint8_t start_minute,
                                             uint8_t end_hour, uint8_t end_minute,
                                             const char *label);

/**
 * @brief 删除时间段
 * 
 * @param med_id 药品ID
 * @param label 时间段标签
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_remove_timeslot(const char *med_id, const char *label);

/**
 * @brief 检查所有药品调度（定期调用）
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_check_all(void);

/**
 * @brief 手动触发药品分发
 * 
 * @param med_id 药品ID
 * @param timeslot_label 时间段标签
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_manual_dispense(const char *med_id, const char *timeslot_label);

/**
 * @brief 重置每日分药标志
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_reset_daily_flags(void);

/**
 * @brief 获取药品调度信息
 * 
 * @param med_id 药品ID
 * @param schedule 调度信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_get_schedule(const char *med_id, medication_schedule_t *schedule);

/**
 * @brief 获取所有药品调度列表
 * 
 * @param schedules 调度数组
 * @param max_count 最大数量
 * @param actual_count 实际获取数量
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_get_all_schedules(medication_schedule_t *schedules,
                                                  uint8_t max_count,
                                                  uint8_t *actual_count);

/**
 * @brief 获取管理器状态
 * 
 * @param manager 管理器状态结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_get_manager_status(medication_scheduler_manager_t *manager);

/**
 * @brief 获取统计信息
 * 
 * @param statistics 统计信息结构体指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_get_statistics(medication_scheduler_statistics_t *statistics);

/**
 * @brief 清空错误记录
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_clear_errors(void);

/**
 * @brief 设置分药完成回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_set_dispense_callback(medication_dispense_callback_t callback);

/**
 * @brief 设置错误回调
 * 
 * @param callback 回调函数指针
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_set_error_callback(medication_error_callback_t callback);

/**
 * @brief 生成药品ID
 * 
 * @param id_buffer ID缓冲区
 * @param buffer_size 缓冲区大小
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_generate_id(char *id_buffer, size_t buffer_size);

/**
 * @brief 获取频率字符串
 * 
 * @param frequency 频率枚举
 * @return const char* 频率字符串
 */
const char* medication_scheduler_get_frequency_string(medication_frequency_e frequency);

/**
 * @brief 获取星期字符串
 * 
 * @param weekday 星期枚举
 * @return const char* 星期字符串
 */
const char* medication_scheduler_get_weekday_string(medication_weekday_e weekday);

/**
 * @brief 打印所有调度信息
 * 
 * @return OPERATE_RET 操作结果
 */
OPERATE_RET medication_scheduler_print_all(void);

/**
 * @brief 运行所有测试用例
 * 
 * @return OPERATE_RET 测试结果
 */
OPERATE_RET medication_scheduler_run_tests(void);

#ifdef __cplusplus
}
#endif

#endif /* __MEDICATION_SCHEDULER_H__ */
