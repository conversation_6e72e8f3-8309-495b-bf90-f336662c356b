/**
 * @file font_chinese_16.h
 * @brief Chinese font 16pt header file
 * @version 1.0
 * @date 2025-07-24
 */

#ifndef FONT_CHINESE_16_H
#define FONT_CHINESE_16_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

/**
 * @brief Chinese font 16pt with UTF-8 support
 * 
 * This font includes:
 * - ASCII characters (0x20-0x7E)
 * - Common Chinese characters (U+4E00-U+9FFF)
 * - Special symbols
 * 
 * Font size: 16px
 * Encoding: UTF-8
 * Format: LVGL font format
 */
extern const lv_font_t font_chinese_16;

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*FONT_CHINESE_16_H*/
