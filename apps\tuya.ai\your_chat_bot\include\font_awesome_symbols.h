#ifndef FONT_AWESOME_SYMBOLS_H
#define FONT_AWESOME_SYMBOLS_H

#define FONT_AWESOME_EMOJI_NEUTRAL     "\xef\x96\xa4"
#define FONT_AWESOME_EMOJI_HAPPY       "\xef\x84\x98"
#define FONT_AWESOME_EMOJI_LAUGHING    "\xef\x96\x9b"
#define FONT_AWESOME_EMOJI_FUNNY       "\xef\x96\x88"
#define FONT_AWESOME_EMOJI_SAD         "\xee\x8e\x84"
#define FONT_AWESOME_EMOJI_ANGRY       "\xef\x95\x96"
#define FONT_AWESOME_EMOJI_CRYING      "\xef\x96\xb3"
#define FONT_AWESOME_EMOJI_LOVING      "\xef\x96\x84"
#define FONT_AWESOME_EMOJI_EMBARRASSED "\xef\x95\xb9"
#define FONT_AWESOME_EMOJI_SURPRISED   "\xee\x8d\xab"
#define FONT_AWESOME_EMOJI_SHOCKED     "\xee\x8d\xb5"
#define FONT_AWESOME_EMOJI_THINKING    "\xee\x8e\x9b"
#define FONT_AWESOME_EMOJI_WINKING     "\xef\x93\x9a"
#define FONT_AWESOME_EMOJI_COOL        "\xee\x8e\x98"
#define FONT_AWESOME_EMOJI_RELAXED     "\xee\x8e\x92"
#define FONT_AWESOME_EMOJI_DELICIOUS   "\xee\x8d\xb2"
#define FONT_AWESOME_EMOJI_KISSY       "\xef\x96\x98"
#define FONT_AWESOME_EMOJI_CONFIDENT   "\xee\x90\x89"
#define FONT_AWESOME_EMOJI_SLEEPY      "\xee\x8e\x8d"
#define FONT_AWESOME_EMOJI_SILLY       "\xee\x8e\xa4"
#define FONT_AWESOME_EMOJI_CONFUSED    "\xee\x8d\xad"
#define FONT_AWESOME_BATTERY_FULL      "\xef\x89\x80"
#define FONT_AWESOME_BATTERY_3         "\xef\x89\x81"
#define FONT_AWESOME_BATTERY_2         "\xef\x89\x82"
#define FONT_AWESOME_BATTERY_1         "\xef\x89\x83"
#define FONT_AWESOME_BATTERY_EMPTY     "\xef\x89\x84"
#define FONT_AWESOME_BATTERY_SLASH     "\xef\x8d\xb7"
#define FONT_AWESOME_BATTERY_CHARGING  "\xef\x8d\xb6"
#define FONT_AWESOME_WIFI              "\xef\x87\xab"
#define FONT_AWESOME_WIFI_FAIR         "\xef\x9a\xab"
#define FONT_AWESOME_WIFI_WEAK         "\xef\x9a\xaa"
#define FONT_AWESOME_WIFI_OFF          "\xef\x9a\xac"
#define FONT_AWESOME_SIGNAL_FULL       "\xef\x80\x92"
#define FONT_AWESOME_SIGNAL_4          "\xef\x9a\x8f"
#define FONT_AWESOME_SIGNAL_3          "\xef\x9a\x8e"
#define FONT_AWESOME_SIGNAL_2          "\xef\x9a\x8d"
#define FONT_AWESOME_SIGNAL_1          "\xef\x9a\x8c"
#define FONT_AWESOME_SIGNAL_OFF        "\xef\x9a\x95"
#define FONT_AWESOME_VOLUME_HIGH       "\xef\x80\xa8"
#define FONT_AWESOME_VOLUME_MEDIUM     "\xef\x9a\xa8"
#define FONT_AWESOME_VOLUME_LOW        "\xef\x80\xa7"
#define FONT_AWESOME_VOLUME_MUTE       "\xef\x9a\xa9"
#define FONT_AWESOME_MUSIC             "\xef\x80\x81"
#define FONT_AWESOME_CHECK             "\xef\x80\x8c"
#define FONT_AWESOME_XMARK             "\xef\x80\x8d"
#define FONT_AWESOME_POWER             "\xef\x80\x91"
#define FONT_AWESOME_GEAR              "\xef\x80\x93"
#define FONT_AWESOME_TRASH             "\xef\x87\xb8"
#define FONT_AWESOME_HOME              "\xef\x80\x95"
#define FONT_AWESOME_IMAGE             "\xef\x80\xbe"
#define FONT_AWESOME_EDIT              "\xef\x81\x84"
#define FONT_AWESOME_PREV              "\xef\x81\x88"
#define FONT_AWESOME_NEXT              "\xef\x81\x91"
#define FONT_AWESOME_PLAY              "\xef\x81\x8b"
#define FONT_AWESOME_PAUSE             "\xef\x81\x8c"
#define FONT_AWESOME_STOP              "\xef\x81\x8d"
#define FONT_AWESOME_MICARROW_LEFT     "\xef\x81\xa0"
#define FONT_AWESOME_ARROW_RIGHT       "\xef\x81\xa1"
#define FONT_AWESOME_ARROW_UP          "\xef\x81\xa2"
#define FONT_AWESOME_ARROW_DOWN        "\xef\x81\xa3"
#define FONT_AWESOME_WARNING           "\xef\x81\xb1"
#define FONT_AWESOME_BELL              "\xef\x83\xb3"
#define FONT_AWESOME_LOCATION          "\xef\x8f\x85"
#define FONT_AWESOME_GLOBE             "\xef\x82\xac"
#define FONT_AWESOME_LOCATION_ARROW    "\xef\x84\xa4"
#define FONT_AWESOME_SD_CARD           "\xef\x9f\x82"
#define FONT_AWESOME_BLUETOOTH         "\xef\x8a\x93"
#define FONT_AWESOME_COMMENT           "\xef\x81\xb5"
#define FONT_AWESOME_AI_CHIP           "\xee\x87\xac"
#define FONT_AWESOME_USER              "\xef\x80\x87"
#define FONT_AWESOME_USER_ROBOT        "\xee\x81\x8b"
#define FONT_AWESOME_DOWNLOAD          "\xef\x80\x99"

#endif
